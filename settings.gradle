rootProject.name='AndroidUIComponents'
include ':app'
include ':libui'

dependencyResolutionManagement {
    repositories {
        maven {
            url "https://artifacts.surya-digital.in/repository/maven-releases/"
            def artifactoryProperties = new Properties()
            artifactoryProperties.load(new FileInputStream(new File("$rootDir/artifactory.properties")))
            credentials {
                username = artifactoryProperties["username"].toString()
                password = artifactoryProperties["password"].toString()
            }
        }
        mavenCentral()
        mavenLocal()
    }
    versionCatalogs {
        libs {
            from("com.suryadigital.leo:version-catalog:$leo_kotlin_runtime_version")
        }
    }
}
