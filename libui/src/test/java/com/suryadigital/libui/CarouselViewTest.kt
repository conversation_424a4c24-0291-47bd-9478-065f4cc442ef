package com.suryadigital.libui

import android.app.Activity
import android.graphics.Color
import android.util.AttributeSet
import com.suryadigital.leo.libui.R
import com.suryadigital.leo.libui.carouselview.CarouselRecyclerView
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.android.controller.ActivityController

@RunWith(RobolectricTestRunner::class)
class CarouselViewTest {
    companion object {
        @JvmStatic
        internal val SELECTED_INDICATOR_RADIUS = 8f

        @JvmStatic
        internal val UNSELECTED_INDICATOR_RADIUS = 7f

        @JvmStatic
        internal val SELECTED_INDICATOR_COLOR = Color.BLACK

        @JvmStatic
        internal val UNSELECTED_INDICATOR_COLOR = Color.GRAY

        @JvmStatic
        internal val PAGE_CHANGE_DURATION: Long = 3000

        @JvmStatic
        internal val INDICATOR_VISIBLE = false
    }

    private lateinit var activityController: ActivityController<Activity>
    private lateinit var activity: Activity

    private lateinit var carouselView: CarouselRecyclerView

    @Before
    fun setUp() {
        activityController = Robolectric.buildActivity(Activity::class.java)
        activity = activityController.get()
        carouselView = CarouselRecyclerView(activity)
    }

    @Test
    fun validateDefaultAttributes() {
        assertEquals(carouselView.attributes.selectedIndicatorRadius, SELECTED_INDICATOR_RADIUS)
        assertEquals(carouselView.attributes.selectedIndicatorColor, SELECTED_INDICATOR_COLOR)
        assertEquals(carouselView.attributes.unSelectedIndicatorColor, UNSELECTED_INDICATOR_COLOR)
        assertEquals(carouselView.attributes.unSelectedIndicatorRadius, UNSELECTED_INDICATOR_RADIUS)
        assertEquals(carouselView.attributes.pageChangeDuration, PAGE_CHANGE_DURATION)
        assertEquals(carouselView.attributes.isIndicatorVisible, INDICATOR_VISIBLE)
    }

    @Test
    fun validateAttributes() {
        val attributeSet: AttributeSet =
            Robolectric
                .buildAttributeSet()
                .addAttribute(R.attr.selectedIndicatorRadius, "10")
                .addAttribute(R.attr.unSelectedIndicatorRadius, "8")
                .addAttribute(R.attr.carouselPageChangeDuration, "5000")
                .addAttribute(R.attr.carouselSelectedIndicatorColor, "@android:color/black")
                .addAttribute(R.attr.unselectedIndicatorColor, "@android:color/white")
                .build()
        carouselView = CarouselRecyclerView(activity, attributeSet)
        assertEquals(carouselView.attributes.selectedIndicatorRadius, 10.0f)
        assertEquals(carouselView.attributes.unSelectedIndicatorRadius, 8.0f)
        assertEquals(carouselView.attributes.selectedIndicatorColor, activity.getColor(android.R.color.black))
        assertEquals(carouselView.attributes.unSelectedIndicatorColor, activity.getColor(android.R.color.white))
        assertEquals(carouselView.attributes.pageChangeDuration, 5000L)
    }

    @Test
    fun showIndicatorTest() {
        carouselView.attributes.isIndicatorVisible = false
        carouselView.showIndicator()
        assertEquals(true, carouselView.attributes.isIndicatorVisible)
    }

    @Test
    fun hideIndicatorTest() {
        carouselView.attributes.isIndicatorVisible = true
        carouselView.hideIndicator()
        carouselView.pauseCarousel()
        assertEquals(false, carouselView.attributes.isIndicatorVisible)
    }
}
