package com.suryadigital.libui

import com.suryadigital.leo.libui.contactpicker.Contact
import com.suryadigital.leo.libui.contactpicker.ContactPickerViewModel
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@RunWith(RobolectricTestRunner::class)
class ContactPickerTest {
    private lateinit var contactPickerViewModel: ContactPickerViewModel

    @Before
    fun setUp() {
        contactPickerViewModel = ContactPickerViewModel()
    }

    @Test
    fun updateSingleContactTest() {
        contactPickerViewModel.updateContact(Contact("Amin", "9977889966"))
        assertTrue { contactPickerViewModel.getContactNumberLiveData().value?.name == "Amin" }
    }

    @Test
    fun updateSelectedContactList() {
        contactPickerViewModel.updateSelectedContactList(
            mutableListOf(
                Contact("Ram", "9823903389"),
                Contact("<PERSON><PERSON><PERSON>", "9823953382"),
                Contact("She<PERSON>", "9823623380"),
            ),
        )
        assertEquals(contactPickerViewModel.getSelectedContactsLiveData().value!!.size, 3)
        assertEquals(contactPickerViewModel.getSelectedContactsLiveData().value!![0].name, "Ram")
    }

    @Test
    fun updateSelectedCountLiveData() {
        contactPickerViewModel.updateSelectedContactCountLiveData(10)
        assertEquals(10, contactPickerViewModel.getSelectedContactCountLiveData().value)
    }
}
