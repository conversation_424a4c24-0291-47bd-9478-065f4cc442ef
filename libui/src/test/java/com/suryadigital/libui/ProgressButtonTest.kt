package com.suryadigital.libui

import android.app.Activity
import android.graphics.Color
import com.suryadigital.leo.libui.R
import com.suryadigital.leo.libui.progressbutton.ProgressButton
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import kotlin.test.assertEquals

@RunWith(RobolectricTestRunner::class)
class ProgressButtonTest {
    private lateinit var activity: Activity
    private lateinit var progressButton: ProgressButton

    @Before
    fun setUp() {
        activity = Robolectric.buildActivity(Activity::class.java).get()
    }

    @Test
    fun testDefaultAttributes() {
        progressButton = ProgressButton(activity, null)
        assertEquals(progressButton.attributes.progressButtonTextColor, Color.BLACK)
        assertEquals(progressButton.attributes.progressButtonBackgroundColor, Color.LTGRAY)
        assertEquals(progressButton.attributes.progressButtonText, "")
        assertEquals(progressButton.attributes.progressButtonElevation, 4f)
        assertEquals(progressButton.attributes.progressButtonRadius, 2f)
    }

    @Test
    fun testCustomAttributes() {
        val attributeSet =
            Robolectric
                .buildAttributeSet()
                .addAttribute(R.attr.buttonTextColor, "@android:color/white")
                .addAttribute(R.attr.buttonText, "submit")
                .addAttribute(R.attr.buttonBackgroundColor, "@android:color/white")
                .addAttribute(R.attr.buttonElevation, "2dp")
                .addAttribute(R.attr.buttonRadius, "2dp")
                .build()
        progressButton = ProgressButton(activity, attributeSet)
        assertEquals(progressButton.attributes.progressButtonTextColor, Color.WHITE)
        assertEquals(progressButton.attributes.progressButtonBackgroundColor, Color.WHITE)
        assertEquals(progressButton.attributes.progressButtonText, "submit")
        assertEquals(progressButton.attributes.progressButtonElevation, 2f)
        assertEquals(progressButton.attributes.progressButtonRadius, 2f)
    }

    @Test
    fun setTextMethodTest() {
        progressButton = ProgressButton(activity, null)
        progressButton.setText("submit")
        assertEquals(progressButton.getText(), "submit")
    }

    @Test
    fun setRadiusMethodTest() {
        progressButton = ProgressButton(activity, null)
        progressButton.setText("submit")
        assertEquals(progressButton.getText(), "submit")
    }
}
