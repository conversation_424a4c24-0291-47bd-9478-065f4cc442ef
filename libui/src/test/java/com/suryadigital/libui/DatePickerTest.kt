package com.suryadigital.libui

import com.suryadigital.leo.libui.datepicker.DatePicker
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import java.time.LocalDate

@RunWith(RobolectricTestRunner::class)
class DatePickerTest {
    private lateinit var datePicker: DatePicker

    @Before
    fun setUp() {
        datePicker = DatePicker()
    }

    @Test
    fun testCurrentDate() {
        val expectedDate = LocalDate.now()
        val actualDate = datePicker.currentDate()
        assertEquals(expectedDate, actualDate)
    }
}
