package com.suryadigital.libui

import android.app.Activity
import android.graphics.Color
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.View
import androidx.core.content.res.ResourcesCompat
import com.suryadigital.leo.libui.R
import com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.android.controller.ActivityController

@RunWith(RobolectricTestRunner::class)
class VerticalImageButtonTest {
    companion object {
        @JvmStatic private val SAMPLE_DIMENSION = 10f

        @JvmStatic private val SAMPLE_TEXT_SIZE = 10

        @JvmStatic private val SAMPLE_RIPPLE_SIZE = 150

        @JvmStatic private val SAMPLE_ICON_HEIGHT_PERCENT = 0.5f

        @JvmStatic private val SAMPLE_LABEL_FONT_STYLE = "bold"

        @JvmStatic private val SAMPLE_LABEL_FONT_FAMILY_IBM_FLEX = "@font/ibm_flex_serif_bold"

        @JvmStatic private val UNIT_SP = "sp"

        @JvmStatic private val UNIT_DP = "dp"

        @JvmStatic private val DEFAULT_ICON_SOURCE = null

        @JvmStatic private val DEFAULT_LABEL_TEXT = ""

        @JvmStatic private val DEFAULT_LABEL_TEXT_SIZE = 14

        @JvmStatic private val DEFAULT_LABEL_TEXT_FONT_FAMILY = null

        @JvmStatic private val DEFAULT_LABEL_TEXT_COLOR = Color.BLACK

        @JvmStatic private val DEFAULT_LABEL_TEXT_FONT_STYLE = Typeface.NORMAL

        @JvmStatic private val DEFAULT_ICON_WIDTH = -1f

        @JvmStatic private val DEFAULT_ICON_PADDING = 0f

        @JvmStatic private val DEFAULT_CORNER_RADIUS = 0f

        @JvmStatic private val DEFAULT_ICON_LABEL_SEPARATION = 0f

        @JvmStatic private val DEFAULT_HEIGHT_PERCENT = 5f

        @JvmStatic private val DEFAULT_ICON_BACKGROUND_COLOR = Color.TRANSPARENT

        @JvmStatic private val DEFAULT_RIPPLE_RADIUS = 100
    }

    private lateinit var activityController: ActivityController<Activity>
    private lateinit var activity: Activity

    private lateinit var verticalImageButton: VerticalImageButton

    @Before
    fun setUp() {
        activityController = Robolectric.buildActivity(Activity::class.java)
        activity = activityController.get()

        verticalImageButton = VerticalImageButton(activity)
    }

    @Test
    fun validateDefaultAttributes() {
        assertEquals(verticalImageButton.attributes.iconWidth, DEFAULT_ICON_WIDTH)
        assertEquals(verticalImageButton.attributes.iconPadding, DEFAULT_ICON_PADDING)
        assertEquals(verticalImageButton.attributes.iconLabelSeparation, DEFAULT_ICON_LABEL_SEPARATION)
        assertEquals(verticalImageButton.attributes.iconBackgroundCornerRadius, DEFAULT_CORNER_RADIUS)
        assertEquals(verticalImageButton.attributes.labelTextSize, DEFAULT_LABEL_TEXT_SIZE)
        assertEquals(verticalImageButton.attributes.iconHeightPercent, DEFAULT_HEIGHT_PERCENT)

        assertEquals(verticalImageButton.attributes.labelText, DEFAULT_LABEL_TEXT)
        assertEquals(verticalImageButton.attributes.labelTextColor, DEFAULT_LABEL_TEXT_COLOR)
        assertEquals(verticalImageButton.attributes.labelTextFontFamily, DEFAULT_LABEL_TEXT_FONT_FAMILY)
        assertEquals(verticalImageButton.attributes.labelTextFontStyle, DEFAULT_LABEL_TEXT_FONT_STYLE)
        assertEquals(verticalImageButton.attributes.iconBackgroundColor, DEFAULT_ICON_BACKGROUND_COLOR)
        assertEquals(verticalImageButton.attributes.iconSource, DEFAULT_ICON_SOURCE)
        assertEquals(verticalImageButton.attributes.rippleRadius, DEFAULT_RIPPLE_RADIUS)
    }

    @Test
    fun validateAttributes() {
        val attributeSet: AttributeSet = Robolectric.buildAttributeSet()
            .addAttribute(R.attr.labelText, "@android:string/ok")
            .addAttribute(R.attr.labelTextColor, "@android:color/black")
            .addAttribute(R.attr.iconSource, "@android:drawable/ic_dialog_alert")
            .addAttribute(R.attr.iconBackgroundColor, "@android:color/black")
            .addAttribute(R.attr.iconHeightPercent, "$SAMPLE_ICON_HEIGHT_PERCENT")
            .addAttribute(R.attr.iconWidth, "$SAMPLE_DIMENSION$UNIT_DP")
            .addAttribute(R.attr.iconPadding, "$SAMPLE_DIMENSION$UNIT_DP")
            .addAttribute(R.attr.labelTextSize, "$SAMPLE_TEXT_SIZE$UNIT_SP")
            .addAttribute(R.attr.labelTextFontStyle, SAMPLE_LABEL_FONT_STYLE)
            .addAttribute(R.attr.labelTextFontFamily, SAMPLE_LABEL_FONT_FAMILY_IBM_FLEX)
            .addAttribute(R.attr.iconLabelSeparation, "$SAMPLE_DIMENSION$UNIT_DP")
            .addAttribute(R.attr.iconBackgroundCornerRadius, "$SAMPLE_DIMENSION$UNIT_DP")
            .addAttribute(R.attr.rippleRadius, "$SAMPLE_RIPPLE_SIZE")
            .build()

        verticalImageButton = VerticalImageButton(activity, attributeSet)

        assertEquals(verticalImageButton.attributes.iconWidth, SAMPLE_DIMENSION)
        assertEquals(verticalImageButton.attributes.iconPadding, SAMPLE_DIMENSION)
        assertEquals(verticalImageButton.attributes.iconLabelSeparation, SAMPLE_DIMENSION)
        assertEquals(verticalImageButton.attributes.iconBackgroundCornerRadius, SAMPLE_DIMENSION)
        assertEquals(verticalImageButton.attributes.labelTextSize, SAMPLE_TEXT_SIZE)
        assertEquals(verticalImageButton.attributes.iconHeightPercent, SAMPLE_ICON_HEIGHT_PERCENT)

        assertEquals(verticalImageButton.attributes.labelText, activity.getString(android.R.string.ok))
        assertEquals(verticalImageButton.attributes.iconBackgroundColor, activity.getColor(android.R.color.black))
        assertEquals(verticalImageButton.attributes.labelTextColor, activity.getColor(android.R.color.black))
        assertEquals(verticalImageButton.attributes.labelTextFontFamily, ResourcesCompat.getFont(activity, R.font.ibm_flex_serif_bold))
        assertEquals(verticalImageButton.attributes.labelTextFontStyle, Typeface.BOLD)
        assertEquals(verticalImageButton.attributes.rippleRadius, SAMPLE_RIPPLE_SIZE)
        assertEquals(
            verticalImageButton.attributes.iconSource?.constantState,
            activity.getDrawable(android.R.drawable.ic_dialog_alert)?.constantState,
        )
    }

    @Test
    fun verticalImageButtonOnClickListenerTest() {
        var isClicked = false
        verticalImageButton.setOnClickListener { isClicked = true }
        verticalImageButton.performClick()

        assert(isClicked)
    }

    @Test
    fun validateSetIcon() {
        verticalImageButton.setIcon(
            activity.getDrawable(android.R.drawable.ic_dialog_alert),
            SAMPLE_DIMENSION,
        )
        assertEquals(verticalImageButton.attributes.iconPadding, SAMPLE_DIMENSION)
        assertEquals(
            verticalImageButton.attributes.iconSource?.constantState,
            activity.getDrawable(android.R.drawable.ic_dialog_alert)?.constantState,
        )
    }

    @Test
    fun validateSetButtonLabel() {
        val typeface: Typeface? = null
        verticalImageButton.setButtonLabel(
            activity.getString(android.R.string.ok),
            SAMPLE_TEXT_SIZE,
            typeface,
            activity.getColor(android.R.color.black),
            Typeface.BOLD_ITALIC,
        )
        assertEquals(verticalImageButton.attributes.labelText, activity.getString(android.R.string.ok))
        assertEquals(verticalImageButton.attributes.labelTextColor, activity.getColor(android.R.color.black))
        assertEquals(verticalImageButton.attributes.labelTextSize, SAMPLE_TEXT_SIZE)
        assertEquals(verticalImageButton.attributes.labelTextFontFamily, typeface)
        assertEquals(verticalImageButton.attributes.labelTextFontStyle, Typeface.BOLD_ITALIC)
    }

    @Test
    fun validateSetLayoutParams() {
        verticalImageButton.setLayoutParams(
            SAMPLE_ICON_HEIGHT_PERCENT,
            SAMPLE_DIMENSION,
            SAMPLE_DIMENSION,
        )
        assertEquals(verticalImageButton.attributes.iconWidth, SAMPLE_DIMENSION)
        assertEquals(verticalImageButton.attributes.iconLabelSeparation, SAMPLE_DIMENSION)
        assertEquals(verticalImageButton.attributes.iconHeightPercent, SAMPLE_ICON_HEIGHT_PERCENT)
    }

    @Test
    fun validateSetIconBackground() {
        verticalImageButton.setIconBackground(
            SAMPLE_DIMENSION,
            activity.getColor(android.R.color.black),
        )
        assertEquals(verticalImageButton.attributes.iconBackgroundCornerRadius, SAMPLE_DIMENSION)
        assertEquals(verticalImageButton.attributes.iconBackgroundColor, activity.getColor(android.R.color.black))
    }

    @Test
    fun validateRippleRadius() {
        verticalImageButton.setBackgroundRipple(SAMPLE_RIPPLE_SIZE)
        assertEquals(verticalImageButton.attributes.rippleRadius, SAMPLE_RIPPLE_SIZE)
    }

    @Test
    fun validateSubViewsInitialization() {
        assertEquals(verticalImageButton.labelTextView.visibility, View.VISIBLE)
        assertEquals(verticalImageButton.iconBackgroundCardView.visibility, View.VISIBLE)
        assertEquals(verticalImageButton.iconImageView.visibility, View.VISIBLE)
    }
}
