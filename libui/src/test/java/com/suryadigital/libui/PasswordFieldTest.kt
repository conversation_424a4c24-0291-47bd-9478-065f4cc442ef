package com.suryadigital.libui

import android.app.Activity
import com.suryadigital.leo.libui.passwordfield.PasswordDoesntMatchRegExException
import com.suryadigital.leo.libui.passwordfield.PasswordNotLongEnoughException
import com.suryadigital.leo.libui.passwordfield.PasswordTextField
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

@RunWith(RobolectricTestRunner::class)
@Config(application = TestApplication::class)
class PasswordFieldTest {

    private lateinit var activityController: ActivityController<Activity>
    private lateinit var activity: Activity
    private lateinit var passwordTextField: PasswordTextField

    @Before
    fun setUp() {
        activityController = Robolectric.buildActivity(Activity::class.java)
        activity = activityController.get()
        passwordTextField = PasswordTextField(activity)
    }

    @Test(expected = PasswordNotLongEnoughException::class)
    fun passwordLengthTest() {
        passwordTextField.setMinimumPasswordLength(5)
        passwordTextField.setText("1OTP")
        passwordTextField.enteredPassword()
    }

    @Test
    fun passwordRegexFailsTest() {
        assertFailsWith<PasswordDoesntMatchRegExException> {
            passwordTextField.setPasswordValidationRegex("^[a-zA-Z]\\w{3,14}\$")
            passwordTextField.setText("1abcd")
            passwordTextField.enteredPassword()
        }
    }

    @Test
    fun passwordRegexTest() {
        passwordTextField.setPasswordValidationRegex("^[a-zA-Z]\\w{3,14}\$")
        passwordTextField.setText("abcd")
        val actual = passwordTextField.enteredPassword()
        assertEquals("abcd", actual)
    }
}
