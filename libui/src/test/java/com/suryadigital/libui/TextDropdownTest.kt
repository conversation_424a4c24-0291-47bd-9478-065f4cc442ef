package com.suryadigital.libui

import android.app.Activity
import android.view.View
import android.widget.AdapterView
import com.suryadigital.leo.libui.textdropdown.TextDropdown
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import kotlin.test.assertFailsWith

@RunWith(RobolectricTestRunner::class)
class TextDropdownTest {
    private lateinit var activity: Activity
    private lateinit var textDropdown: TextDropdown

    @Before
    fun setUp() {
        activity = Robolectric.buildActivity(Activity::class.java).get()
        textDropdown = TextDropdown(activity)
    }

    @Test
    fun setDropdownItemsTest() {
        textDropdown.setAdapter(
            SecurityQuestionsTestAdapter(
                listOf(
                    "one",
                    "two",
                    "three",
                ),
            ),
        )
        assertEquals(3, textDropdown.adapter.count)
    }

    @Test
    fun defaultIndexSelectionTest() {
        textDropdown.setAdapter(
            SecurityQuestionsTestAdapter(
                listOf(
                    "one",
                    "two",
                ),
            ),
        )
        assertEquals(0, textDropdown.selectedItemPosition)
        assertEquals("one", textDropdown.adapter.getItem(textDropdown.selectedItemPosition))
        textDropdown.setSelection(1)
        assertEquals("two", textDropdown.adapter.getItem(textDropdown.selectedItemPosition))
    }

    @Test
    fun addListenerThenSelectItemTest() {
        var expectedPosition = -1
        textDropdown.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onNothingSelected(parent: AdapterView<*>?) {}

                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long,
                ) {
                    expectedPosition = position
                }
            }
        textDropdown.setAdapter(
            SecurityQuestionsTestAdapter(
                listOf(
                    "one",
                    "two",
                    "three",
                ),
            ),
        )
        // Asserts that by default listener won't be called.
        assertEquals(-1, expectedPosition)

        // Asserts that on selection listener will be called.
        textDropdown.setSelection(1)
        assertEquals(1, expectedPosition)
    }

    @Test
    fun callbackWithoutAdapterTest() {
        var expectedPosition = 0
        textDropdown.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onNothingSelected(parent: AdapterView<*>?) {}

                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long,
                ) {
                    expectedPosition = position
                }
            }
        // No callback will be there as adapter has not been set.
        textDropdown.setSelection(2)
        assertFailsWith<AssertionError> { assertEquals(2, expectedPosition) }
        textDropdown.setAdapter(
            SecurityQuestionsTestAdapter(
                listOf(
                    "one",
                    "two",
                    "three",
                ),
            ),
        )
        // Callback will be there as adapter is set.
        textDropdown.setSelection(1)
        assertEquals(1, expectedPosition)
    }
}
