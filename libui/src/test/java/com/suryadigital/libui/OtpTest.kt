package com.suryadigital.libui

import com.suryadigital.leo.libui.otptextfield.parseFirstNdigitOTPNumberInMessage
import org.junit.Assert.assertEquals
import org.junit.Test

class OtpTest {

    @Test
    fun parseOTPTest() {
        val message = "Your otp is 123456"
        val message2 = "Your otp is 12345"
        val otp = parseFirstNdigitOTPNumberInMessage(message, 6)
        val otp2 = parseFirstNdigitOTPNumberInMessage(message2, 5)
        assertEquals("123456", otp)
        assertEquals("12345", otp2)
    }
}
