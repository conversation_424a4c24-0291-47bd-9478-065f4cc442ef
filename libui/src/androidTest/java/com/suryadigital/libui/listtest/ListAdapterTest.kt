package com.suryadigital.libui.listtest

import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class ListAdapterTest {
    private lateinit var testList: ArrayList<SampleModel>
    private lateinit var testAdapter: SampleAdapter

    @Before
    fun setup() {
        testList = arrayListOf()
        for (i in 1..10) {
            testList.add(SampleModel("Item $i", i))
        }

        testAdapter = SampleAdapter(testList)
    }

    @Test
    fun itemCountTest() {
        val count = testAdapter.itemCount
        Assert.assertEquals(10, count)
    }

    @Test
    fun searchItemCountTest() {
        testAdapter.filter("Item 7")
        val count = testAdapter.itemCount
        Assert.assertEquals(1, count)
    }

    @Test
    fun searchTest() {
        testAdapter.filter("Item 7")
        val sampleData = testAdapter.filterItemList[0]
        Assert.assertEquals(7, sampleData.number)
    }
}
