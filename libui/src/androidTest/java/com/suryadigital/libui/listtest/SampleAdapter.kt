package com.suryadigital.libui.listtest

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.suryadigital.leo.libui.R
import com.suryadigital.leo.libui.listview.ListAdapter

class SampleAdapter(private val itemList: List<SampleModel>) : ListAdapter<SampleModel, RecyclerView.ViewHolder>(itemList) {

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        val data = filterItemList[position]
        (holder as TestViewHolder).testTitle.text = data.title
    }

    override fun filter(query: String) {
        filterItemList.clear()
        if (query.isEmpty()) {
            filterItemList.addAll(itemList)
        } else {
            itemList.filterTo(filterItemList) {
                it.title.contains(query, true)
            }
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.test_item_sample, parent, false)
        return TestViewHolder(view)
    }
}
