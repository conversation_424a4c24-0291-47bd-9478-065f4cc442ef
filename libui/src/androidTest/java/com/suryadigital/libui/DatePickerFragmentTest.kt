package com.suryadigital.libui

import androidx.fragment.app.testing.launchFragment
import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.suryadigital.leo.libui.datepicker.DatePicker
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.time.LocalDate

@RunWith(AndroidJUnit4::class)
class DatePickerFragmentTest {

    private var datePicker: DatePicker? = null

    @Before
    fun setUp() {
        datePicker = DatePicker()
        launchFragment(instantiate = { datePicker!! })
    }

    @Test
    fun testOkButtonTap() {
        var localDate: LocalDate? = null
        datePicker?.setDateChangeListener(object : DatePicker.OnDateChangeListener {
            override fun onDateChanged(date: LocalDate) {
                localDate = date
            }

            override fun dialogCancelled() {
            }
        })
        Espresso.onView(withId(android.R.id.button1)).perform(click())
        Assert.assertTrue(LocalDate.now() == localDate)
    }

    @Test
    fun testCancelButtonTap() {
        var isCancelled = false
        datePicker?.setDateChangeListener(object : DatePicker.OnDateChangeListener {
            override fun onDateChanged(date: LocalDate) {
            }

            override fun dialogCancelled() {
                isCancelled = true
            }
        })
        Espresso.onView(withId(android.R.id.button2)).perform(click())
        Assert.assertTrue(isCancelled)
    }
}
