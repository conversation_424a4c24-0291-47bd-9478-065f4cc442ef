<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="TestAppTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
    </style>

    <style name="DatePickerDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="colorAccent">@color/design_default_color_primary</item>
        <item name="colorControlNormal">@color/design_default_color_primary</item>
    </style>

    <style name="LandingScreenTheme">
        <item name="android:background">@color/landingScreenBgColor</item>
    </style>

    <style name="KeyboardStyle">
        <item name="android:textColor">@color/name_label_color</item>
        <item name="android:textSize">30sp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_rowWeight">1</item>
        <item name="android:layout_columnWeight">1</item>
    </style>

    <style name="PrimaryButton" parent="Widget.MaterialComponents.Button">
        <item name="android:height">@dimen/button_height</item>
        <item name="android:textSize">@dimen/text_12sp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:background">@drawable/bg_button_4dp_primary</item>
    </style>

</resources>
