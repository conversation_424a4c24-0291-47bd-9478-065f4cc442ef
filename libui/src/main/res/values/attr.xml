<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="VerticalImageButton">
        <attr name="labelText" format="string|reference" />
        <attr name="labelTextSize" format="dimension" />
        <attr name="labelTextColor" format="color" />
        <attr name="labelTextFontFamily" format="reference" />
        <attr name="iconSource" format="integer" />
        <attr name="iconWidth" format="dimension" />
        <attr name="iconLabelSeparation" format="dimension" />
        <attr name="iconBackgroundCornerRadius" format="dimension" />
        <attr name="iconBackgroundColor" format="color" />
        <attr name="iconPadding" format="dimension" />
        <attr name="iconHeightPercent" format="float" />
        <attr name="rippleRadius" format="integer" />

        <attr name="labelTextFontStyle" format="enum">
            <enum name="regular" value="0" />
            <enum name="bold" value="1" />
            <enum name="italic" value="2" />
            <enum name="bold_italic" value="3" />
        </attr>
    </declare-styleable>
</resources>
