<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ViewPager">
        <attr name="selectedIndicatorColor" format="color" />
        <attr name="autoScroll" format="boolean" />
        <attr name="stopScrollingOnComplete" format="boolean" />
        <attr name="hiddenIndicator" format="boolean" />
        <attr name="previewPages" format="boolean" />
        <attr name="pageChangeDuration" format="integer" />
        <attr name="scrollDuration" format="integer" />
    </declare-styleable>

    <declare-styleable name="LandingScreen">
        <attr name="selectedPageColor" format="color" />
        <attr name="skipButtonColor" format="color" />
        <attr name="nextButtonColor" format="color" />
        <attr name="finishButtonTextColor" format="color"/>
        <attr name="landingScreenBackgroundColor" format="color"/>
        <attr name="titleColor" format="color"/>
        <attr name="subTitleColor" format="color"/>
        <attr name="finishButtonText" format="string"/>
        <attr name="finishButtonBackground" format="integer"/>
        <attr name="titleTextSize" format="dimension"/>
        <attr name="subTitleTextSize" format="dimension"/>
        <attr name="shouldShowOnlyFinishButton" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="CarouselRecyclerView">
        <attr name="carouselSelectedIndicatorColor" format="color" />
        <attr name="carouselPageChangeDuration" format="integer" />
        <attr name="unselectedIndicatorColor" format="color" />
        <attr name="selectedIndicatorRadius" format="float" />
        <attr name="unSelectedIndicatorRadius" format="float" />
        <attr name="indicatorVisible" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ProgressButton">
        <attr name="buttonText" format="string"/>
        <attr name="buttonTextFontStyle" format="enum">
            <enum name="regular" value="0" />
            <enum name="bold" value="1" />
            <enum name="italic" value="2" />
            <enum name="bold_italic" value="3" />
        </attr>
        <attr name="buttonTextColor" format="color"/>
        <attr name="buttonRadius" format="dimension"/>
        <attr name="buttonBackgroundColor" format="color"/>
        <attr name="buttonElevation" format="dimension"/>
    </declare-styleable>
</resources>
