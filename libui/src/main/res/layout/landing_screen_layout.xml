<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/landing_screen_layout"
    style="@style/LandingScreenTheme"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/landing_screen_view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/indicator_height"
            android:layout_gravity="bottom|center"
            android:layout_marginBottom="20dp"
            app:layout_constraintBottom_toTopOf="@id/skip"
            app:layout_constraintEnd_toStartOf="@id/next"
            app:layout_constraintStart_toEndOf="@id/skip"
            app:tabBackground="@drawable/default_indicator"
            app:tabIndicator="@drawable/indicator_selector"
            app:tabIndicatorHeight="@dimen/indicator_height"
            app:tabMaxWidth="16dp" />

        <Button
            android:id="@+id/skip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:background="@color/transparent"
            android:text="@string/skip"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

        <Button
            android:id="@+id/next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:background="@color/transparent"
            android:text="@string/next"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/finish"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:text="@string/finish"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:background="@drawable/bg_button_4dp_primary"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
