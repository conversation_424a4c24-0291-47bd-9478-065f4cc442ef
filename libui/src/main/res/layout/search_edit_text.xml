<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/button_height"
    android:layout_margin="@dimen/dimen_16dp"
    android:background="@drawable/search_bar_background">

    <ImageView
        android:id="@+id/ic_search_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:importantForAccessibility="no"
        android:padding="@dimen/dimen_8dp"
        android:src="@drawable/ic_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/searchEditText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/searchEditText"
        android:layout_width="0dp"
        android:layout_height="@dimen/button_height"
        android:backgroundTint="@android:color/transparent"
        android:drawablePadding="@dimen/dimen_10dp"
        android:gravity="center_vertical"
        android:hint="@string/label_search"
        android:inputType="phone"
        android:padding="@dimen/dimen_8dp"
        android:textCursorDrawable="@drawable/cursor_color_drawable"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ic_cross_iv"
        app:layout_constraintStart_toEndOf="@id/ic_search_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ic_cross_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/cd_clear_text"
        android:padding="@dimen/dimen_8dp"
        android:src="@drawable/ic_cross"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/searchEditText"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
