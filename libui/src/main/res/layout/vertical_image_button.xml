<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/vertical_image_button_wrapper"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/icon_background_card"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="5"
        android:elevation="0dp">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:contentDescription="@string/vertical_image_button_icon_content_description" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/button_label"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_weight="5"
        android:gravity="center_horizontal" />
</LinearLayout>
