<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.suryadigital.leo.libui.contactpicker.ContactPicker">

    <include layout="@layout/search_edit_text" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <ListView
            android:id="@+id/contactList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dimen_8dp"
            android:layout_marginTop="16dp"
            android:divider="@android:color/transparent" />

        <LinearLayout
            android:id="@+id/request_permission_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dimen_16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/request_contact_permission_desc"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/request_permission_button"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:text="@string/allow_contacts_permission" />

        </LinearLayout>

    </FrameLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/continueButton"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_gravity="bottom|center"
        android:layout_margin="@dimen/dimen_16dp"
        android:backgroundTint="@color/design_default_color_primary"
        android:text="@string/continue_btn"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>
