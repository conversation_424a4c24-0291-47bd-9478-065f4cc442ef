<?xml version="1.0" encoding="utf-8"?>
<GridLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/kb_grid"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="300dp"
    android:columnCount="3"
    android:padding="24dp"
    android:theme="@style/KeyboardStyle"
    tools:context=".numberkeyboard.NumberKeyboardView">

    <TextView
        android:id="@+id/one"
        android:text="@string/label_1" />

    <TextView
        android:id="@+id/two"
        android:text="@string/label_2" />

    <TextView
        android:id="@+id/three"
        android:text="@string/label_3" />

    <TextView
        android:id="@+id/four"
        android:text="@string/label_4" />

    <TextView
        android:id="@+id/five"
        android:text="@string/label_5" />

    <TextView
        android:id="@+id/six"
        android:text="@string/label_6" />

    <TextView
        android:id="@+id/seven"
        android:text="@string/label_7" />

    <TextView
        android:id="@+id/eight"
        android:text="@string/label_8" />

    <TextView
        android:id="@+id/nine"
        android:text="@string/label_9" />

    <TextView />

    <TextView
        android:id="@+id/zero"
        android:text="@string/label_0" />

    <ImageButton
        android:id="@+id/delete"
        android:background="@android:color/transparent"
        android:contentDescription="@string/label_keyboard_delete_button"
        android:src="@drawable/ic_delete_kb" />

</GridLayout>
