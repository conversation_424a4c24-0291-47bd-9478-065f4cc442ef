<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/progress_button_root_cv"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="4dp"
    android:stateListAnimator="@animator/button_state_list_animator">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingStart="@dimen/default_margin_horizonatal"
        android:paddingTop="@dimen/default_margin_vertical"
        android:paddingEnd="@dimen/default_margin_horizonatal"
        android:paddingBottom="@dimen/default_margin_vertical">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_centerInParent="true"
            android:layout_marginEnd="16dp"
            android:layout_toStartOf="@id/progress_tv"
            android:indeterminate="true"
            android:indeterminateTint="@color/black"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/progress_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:lines="1"
            android:textColor="@color/black"
            android:textSize="@dimen/progress_button_default_text_size"
            tools:text="Title" />
    </RelativeLayout>

</androidx.cardview.widget.CardView>
