<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="4dp">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.suryadigital.leo.libui.viewpager.ViewPagerContentView
                android:id="@+id/viewPagerContentView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/indicator_height"
                android:layout_gravity="bottom|center"
                android:layout_marginBottom="10dp"
                app:tabGravity="center"
                android:alpha="0.5"
                app:tabBackground="@drawable/default_indicator"
                app:tabIndicator="@drawable/indicator_selector"
                app:tabIndicatorHeight="@dimen/indicator_height"
                app:tabMaxWidth="16dp"/>

        </FrameLayout>

    </androidx.cardview.widget.CardView>
</merge>
