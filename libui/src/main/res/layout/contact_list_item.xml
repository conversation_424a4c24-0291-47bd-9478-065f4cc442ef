<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dimen_16dp"
    android:background="@drawable/list_row_selector">

    <androidx.cardview.widget.CardView
        android:id="@+id/profile_cv"
        android:layout_width="@dimen/dimen_action_bar_size"
        android:layout_height="@dimen/dimen_action_bar_size"
        app:cardCornerRadius="@dimen/dimen_contact_image_radius"
        app:cardElevation="@dimen/zero_dimen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/contact_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/contact_photo"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_person_black_24dp" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/contact_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_toEndOf="@id/profile_cv"
        android:textColor="@color/name_label_color"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toTopOf="@id/mobile_number"
        app:layout_constraintStart_toEndOf="@id/profile_cv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Ross Taylor" />

    <TextView
        android:id="@+id/mobile_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/contact_name"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_toEndOf="@id/profile_cv"
        android:textColor="@color/mobile_number_label_color"
        android:textSize="@dimen/text_12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/profile_cv"
        app:layout_constraintTop_toBottomOf="@id/contact_name"
        tools:text="9971468242" />

</androidx.constraintlayout.widget.ConstraintLayout>
