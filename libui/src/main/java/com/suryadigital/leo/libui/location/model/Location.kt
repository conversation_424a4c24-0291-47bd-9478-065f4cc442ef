package com.suryadigital.leo.libui.location.model

/**
 * This data class holds location information like coordinates (Latitude [lat], Longitude [long]) and
 * a [thumbnail].
 *
 * Set the thumbnail to load on map as Marker, otherwise default map marker will be loaded.
 *
 * See [LocationMarkerSource] for more details on supported types for [thumbnail].
 */
data class Location(
    val title: String,
    val lat: Double,
    val long: Double,
    val thumbnail: LocationMarkerSource? = null,
)
