package com.suryadigital.leo.libui.contactpicker

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Bundle
import android.text.InputType
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ListView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.google.android.material.button.MaterialButton
import com.suryadigital.leo.libui.R
import com.suryadigital.leo.libui.utils.launchOnMainDispatcher

/**
 * Implements a contact picker which provides single selection as well as multiple selection mode. the default selection
 * mode is single selection mode.
 * ContactPickerSelectionMode : Sets the contacts selection mode.
 * */
class ContactPicker :
    Fragment(),
    AdapterView.OnItemClickListener,
    Filterable {
    interface PermissionRequestListener {
        fun requestPermission()
    }

    private var contactPickerSelectionMode: ContactPickerSelectionMode =
        ContactPickerSelectionMode.SINGLE_SELECTION_CHOICE_MODE
    private lateinit var contactPickerViewModel: ContactPickerViewModel
    private lateinit var onCompletion: () -> Unit
    private lateinit var continueButton: MaterialButton
    private lateinit var clearText: ImageView
    private lateinit var searchET: EditText
    private lateinit var contactsList: ListView
    private val selectedContacts: MutableList<Contact> = mutableListOf()
    private var contactAdapter: ContactListAdapter? = null
    private lateinit var requestPermissionLayout: LinearLayout
    private lateinit var requestPermissionButton: MaterialButton
    private var permissionRequestListener: PermissionRequestListener? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? = layoutInflater.inflate(R.layout.layout_contact_picker, container, false)

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        continueButton = view.findViewById(R.id.continueButton)
        clearText = view.findViewById(R.id.ic_cross_iv)
        searchET = view.findViewById(R.id.searchEditText)
        contactsList = view.findViewById(R.id.contactList)
        requestPermissionLayout = view.findViewById(R.id.request_permission_layout)
        requestPermissionButton = view.findViewById(R.id.request_permission_button)
        requestPermissionButton.setOnClickListener {
            permissionRequestListener?.requestPermission()
        }
        contactsList.onItemClickListener = this
        setMultiSelectionMode()
        searchET.addTextChangedListener(
            object : EditTextWatcher() {
                override fun onTextChanged(s: String) {
                    if (s.isNotEmpty() || s.isNotBlank()) {
                        clearText.visibility = View.VISIBLE
                    } else {
                        clearText.visibility = View.GONE
                    }
                    contactAdapter?.filter(s)
                }
            },
        )
        clearText.setOnClickListener {
            searchET.setText("")
        }
        setOnBackPressed(view)
    }

    override fun onResume() {
        super.onResume()
        initAdapter()
    }

    private fun setOnBackPressed(view: View) {
        view.isFocusableInTouchMode = true
        view.requestFocus()
        view.setOnKeyListener { _, keyCode, _ ->
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                if (::contactPickerViewModel.isInitialized) {
                    contactPickerViewModel.isMultiSelectionModeEnabled = false
                }
            }
            false
        }
    }

    private fun setMultiSelectionMode() {
        if (::contactPickerViewModel.isInitialized &&
            contactPickerViewModel.isMultiSelectionModeEnabled &&
            ::continueButton.isInitialized
        ) {
            continueButton.visibility = View.VISIBLE
            continueButton.setOnClickListener {
                contactPickerViewModel.isMultiSelectionModeEnabled = false
                contactPickerViewModel.updateSelectedContactList(selectedContacts)
                onCompletion()
            }
        }
    }

    override fun onItemClick(
        parent: AdapterView<*>?,
        view: View?,
        position: Int,
        id: Long,
    ) {
        val contactAdapter = parent?.adapter as ContactListAdapter
        val contact = contactAdapter.filterContactList[position]
        toggleContactSelection(contact)
        if (contact.isSelected) {
            selectedContacts.add(contact)
        } else {
            selectedContacts.remove(contact)
        }
        if (!contactPickerViewModel.isMultiSelectionModeEnabled) {
            clearSelectedContact(contactAdapter, contact)
            contactPickerViewModel.updateContact(contact)
            onCompletion()
        } else {
            contactPickerViewModel.updateSelectedContactCountLiveData(selectedContacts.size)
        }
        contactAdapter.notifyDataSetChanged()
    }

    private fun clearSelectedContact(
        contactAdapter: ContactListAdapter,
        contact: Contact,
    ) {
        contactAdapter.filterContactList.forEach {
            if (it.isSelected && it != contact) {
                it.isSelected = false
            }
        }
    }

    private fun toggleContactSelection(contact: Contact) {
        contact.isSelected = !contact.isSelected
    }

    override fun filter(queryString: String) {
        contactAdapter?.filter(queryString)
    }

    fun setPermissionListener(permissionRequestListener: PermissionRequestListener) {
        this.permissionRequestListener = permissionRequestListener
    }

    fun setArguments(
        contactPickerViewModel: ContactPickerViewModel,
        onCompletion: () -> Unit,
        contactListAdapter: ContactListAdapter? = null,
        contactPickerSelectionMode: ContactPickerSelectionMode = ContactPickerSelectionMode.SINGLE_SELECTION_CHOICE_MODE,
    ) {
        this.contactPickerViewModel = contactPickerViewModel
        this.onCompletion = onCompletion
        contactAdapter = contactListAdapter
        this.contactPickerSelectionMode = contactPickerSelectionMode
        if (contactPickerSelectionMode == ContactPickerSelectionMode.MULTIPLE_SELECTION_CHOICE_MODE) {
            contactPickerViewModel.isMultiSelectionModeEnabled = true
        }
        setMultiSelectionMode()
    }

    fun initAdapter() {
        if (contactAdapter == null) {
            contactAdapter = DefaultContactListAdapter(requireContext())
        } else {
            Log.w("ContactListAdapter", "Read contacts permission is not granted")
        }
        launchOnMainDispatcher((contactAdapter as ContactListAdapter)::initContactsList)
        contactsList.adapter = contactAdapter
        if (checkContactPermission(requireContext())) {
            requestPermissionLayout.visibility = View.GONE
            searchET.inputType = InputType.TYPE_CLASS_TEXT
            if (::contactPickerViewModel.isInitialized && contactPickerViewModel.isMultiSelectionModeEnabled) {
                continueButton.visibility = View.VISIBLE
            }
        } else {
            continueButton.visibility = View.GONE
            requestPermissionLayout.visibility = View.VISIBLE
            searchET.inputType = InputType.TYPE_CLASS_PHONE
        }
    }
}

/**
 * This function helps to check if we have contact read permission.
 * @param context : context used by [ContextCompat] to check the permission.
 * */
fun checkContactPermission(context: Context): Boolean =
    ContextCompat.checkSelfPermission(
        context,
        Manifest.permission.READ_CONTACTS,
    ) == PackageManager.PERMISSION_GRANTED

enum class ContactPickerSelectionMode {
    SINGLE_SELECTION_CHOICE_MODE,
    MULTIPLE_SELECTION_CHOICE_MODE,
}
