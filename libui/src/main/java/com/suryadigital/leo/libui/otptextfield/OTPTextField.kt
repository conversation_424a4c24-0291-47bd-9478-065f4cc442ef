package com.suryadigital.leo.libui.otptextfield

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.AttributeSet
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.auth.api.phone.SmsRetrieverClient
import `in`.aabhasjindal.otptextview.OtpTextView
import java.util.regex.Pattern

class OTPTextField
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : OtpTextView(context, attrs, defStyleAttr) {
        private var otpConfiguration: OTPConfiguration? = null
        private var autoOTPReadListener: AutoOTPReadListener? = null

        /**
         * Method is used to set the configuration for auto otp read.
         * @param otpConfiguration : Sets the otp configurations.
         * */
        fun setConfiguration(otpConfiguration: OTPConfiguration) {
            this.otpConfiguration = otpConfiguration
        }

        /**
         * Method set's the auto read otp listener and provides the required callback methods to users.
         * @param autoOTPReadListener : Sets the auto read otp listener.
         * */
        fun setAutoReadOtpListener(autoOTPReadListener: AutoOTPReadListener) {
            this.autoOTPReadListener = autoOTPReadListener
        }

        // Suppressing linting check here because for Android versions below API 33 (TIRAMISU), the registerReceiver() method does not support the 'flags' parameter.
        // Although we're registering for an unprotected broadcast (SMS_RETRIEVED_ACTION), we cannot specify RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED here.
        // This usage is safe for pre-33 devices and required for backward compatibility.

        /**
         * Starts the otp reading as per the given configuration.
         * Note that [AutoOTPReadListener] and [OTPConfiguration] must be set before starting the otp listener.
         * */
        @SuppressLint("UnspecifiedRegisterReceiverFlag")
        fun startOtpReading() {
            autoOTPReadListener?.let {
                otpConfiguration?.apply {
                    val client: SmsRetrieverClient = SmsRetriever.getClient(context)
                    if (otpReadType == OtpReadType.MANUAL_ENTRY) return
                    val task =
                        if (otpReadType == OtpReadType.AUTO_READ_WITH_CONSENT_AND_MANUAL_ENTRY) {
                            client.startSmsUserConsent(null)
                        } else {
                            client.startSmsRetriever()
                        }

                    task.addOnSuccessListener { _ ->
                        val receiverOTPListener: SmsBroadcastReceiver.ReceiverOTPListener =
                            object : SmsBroadcastReceiver.ReceiverOTPListener {
                                override fun onSMSReceived(message: String) {
                                    it.onSuccess(message)
                                }

                                override fun onTimeOut() {
                                    it.onFailure(OTPFailureException("Timeout occurred"))
                                }

                                override fun onConsentActivityStart(
                                    intent: Intent,
                                    requestCode: Int,
                                ) {
                                    it.onConsentIntentGenerated(intent, requestCode)
                                }
                            }
                        val broadcastReceiver =
                            SmsBroadcastReceiver(otpReadType == OtpReadType.AUTO_READ_WITH_CONSENT_AND_MANUAL_ENTRY)
                        broadcastReceiver.setOtpListener(receiverOTPListener)
                        try {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                context.registerReceiver(
                                    broadcastReceiver,
                                    IntentFilter(
                                        SmsRetriever.SMS_RETRIEVED_ACTION,
                                    ),
                                    SmsRetriever.SEND_PERMISSION,
                                    null,
                                    Context.RECEIVER_EXPORTED,
                                )
                            } else {
                                context.registerReceiver(
                                    broadcastReceiver,
                                    IntentFilter(
                                        SmsRetriever.SMS_RETRIEVED_ACTION,
                                    ),
                                    SmsRetriever.SEND_PERMISSION,
                                    null,
                                )
                            }
                        } catch (e: IllegalStateException) {
                            it.onFailure(
                                OTPBroadcastReceiverException(
                                    "Failed to register sms receiver",
                                    e,
                                ),
                            )
                        }
                    }
                    task.addOnFailureListener { e ->
                        it.onAutoReadOTPFailed(
                            OTPBroadcastReceiverException(
                                "SmsRetrieverClient failed to complete sms user consent or sms retriever task",
                                e,
                            ),
                        )
                    }
                }
            }
        }

        companion object {
            const val CREDENTIAL_PICKER_REQUEST: Int = 1
        }
    }

/**
 * This data class models the OTP configuration.
 * @param otpReadType : Sets the otp read type, it can be manual, auto read or auto read with consent.
 * */

data class OTPConfiguration(
    val otpReadType: OtpReadType = OtpReadType.AUTO_READ_AND_MANUAL_ENTRY,
    val otpLength: Int = 6,
)

enum class OtpReadType {
    MANUAL_ENTRY,

    /**
     * This will auto read the otp without user consent, for more details follow the link
     * https://developers.google.com/identity/sms-retriever/overview
     * */
    AUTO_READ_AND_MANUAL_ENTRY,

    /**
     * This will auto read the otp with user consent, for more details follow the link
     * https://developers.google.com/identity/sms-retriever/user-consent/overview
     * */
    AUTO_READ_WITH_CONSENT_AND_MANUAL_ENTRY,
}

/**
 * The is a helper function to parse the otp in specified format,
 * it will extract first [otpLength] digit number from the message if available else it will return null.
 *
 * @param message : The raw message from which otp needs to be extracted.
 * @param otpLength : Sets the length of otp.
 * @return : Extracted OTP if available, else null.
 * */
fun parseFirstNdigitOTPNumberInMessage(
    message: String,
    otpLength: Int,
): String? {
    val patternFormat = String.format("(|^)\\d{$otpLength}")
    Pattern.compile(patternFormat).apply {
        val matcher = matcher(message)
        return if (matcher.find()) {
            matcher.group(0)
        } else {
            null
        }
    }
}
