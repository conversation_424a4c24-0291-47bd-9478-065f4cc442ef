package com.suryadigital.leo.libui.passwordfield

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import androidx.annotation.ColorInt
import com.google.android.material.textfield.TextInputLayout

class PasswordInputLayout : TextInputLayout {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    )

    init {
        setEndIconActivated(true)
        endIconMode = END_ICON_PASSWORD_TOGGLE
    }

    /**
     * This method will change the tint of password eye icon.
     * @param tintColor : Sets color of eye icon
     * */
    fun setPasswordIconTint(@ColorInt tintColor: Int) {
        arrayOf(
            intArrayOf(android.R.attr.state_activated),
        ).apply {
            val colorList = intArrayOf(tintColor)
            val colorStateList = ColorStateList(this, colorList)
            setEndIconTintList(colorStateList)
        }
    }

    /**
     * This method will show or hide the password icon button, if true then it's visible else it's hidden.
     * @param enable : Sets the icon visiblity property.
     * */
    fun displayShowHidePasswordButton(enable: Boolean) {
        endIconMode = if (enable) {
            setEndIconActivated(true)
            END_ICON_PASSWORD_TOGGLE
        } else {
            setEndIconActivated(false)
            END_ICON_NONE
        }
    }
}
