package com.suryadigital.leo.libui.carouselview

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.annotation.ColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.CompletableJob
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Implements carousel view having infinite scroll behaviour & supported page indicator.
 *
 * @attribute selectedIndicatorRadius : Sets the radius of selected page indicator
 * @attribute pageChangeDuration : Sets the time interval for auto scroll to next page
 * @attribute unSelectedIndicatorRadius : Sets the default radius of page indicator
 * @attribute selectedIndicatorColor : Sets the color of selected page indicator
 * @attribute unSelectedIndicatorColor : Sets the color of default page indicator
 * @attribute isIndicatorVisible : Sets the visibility of page indicator
 * */

class CarouselRecyclerView
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : RecyclerView(context, attrs, defStyleAttr) {
        private var autoScrollJob: CompletableJob? = null
        private lateinit var recyclerViewAdapter: CarouselAdapter<*, *>
        private var itemDecoration: CirclePagerIndicatorDecoration? = null
        internal val attributes: CarouselAttributes = CarouselAttributes(context, attrs)
        private var isAutoScrollable: Boolean = false
        private var onCarouselTouchListener: OnCarouselTouchListener? = null

        init {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            PagerSnapHelper().attachToRecyclerView(this)
        }

        private fun initAttributes(carouselAttributes: CarouselAttributes) {
            itemDecoration?.setUnelectedIndicatorRadius(carouselAttributes.unSelectedIndicatorRadius)
            itemDecoration?.setSelectedIndicatorRadius(carouselAttributes.selectedIndicatorRadius)
            itemDecoration?.setUnselectedIndicatorColor(carouselAttributes.selectedIndicatorColor)
            itemDecoration?.setUnselectedIndicatorColor(carouselAttributes.unSelectedIndicatorColor)
        }

        /**
         * Override the touch and hold behaviour, stops the carousel on [MotionEvent.ACTION_DOWN]
         * and play when user release the touch [MotionEvent.ACTION_UP].
         * */
        override fun dispatchTouchEvent(e: MotionEvent): Boolean {
            when (e.action) {
                MotionEvent.ACTION_UP -> {
                    if (isAutoScrollable) {
                        playCarousel()
                    }
                    onCarouselTouchListener?.onUpTouchAction()
                }

                MotionEvent.ACTION_DOWN -> {
                    pauseCarousel()
                    onCarouselTouchListener?.onDownTouchAction()
                }

                MotionEvent.ACTION_MOVE -> {
                    if (isAutoScrollable) {
                        pauseCarousel()
                    }
                    onCarouselTouchListener?.onMoveTouchAction()
                }

                MotionEvent.ACTION_CANCEL -> {
                    playCarousel()
                    onCarouselTouchListener?.onCancelTouchAction()
                }
            }
            parent.requestDisallowInterceptTouchEvent(true)
            return super.dispatchTouchEvent(e)
        }

        /**
         * Implements the page change with the help of recycler view layout manager, find the last visible item
         * on the screen and moves to next page to it.
         * */
        private suspend fun scrollToNext() {
            if (adapter != null) {
                withContext(Dispatchers.Main) {
                    (layoutManager as LinearLayoutManager).let { layoutManager ->
                        val nextPosition = layoutManager.findLastCompletelyVisibleItemPosition() + 1
                        val scrollerObject = LinearSmoothScroller(context)
                        scrollerObject.targetPosition = nextPosition
                        layoutManager.startSmoothScroll(scrollerObject)
                    }
                }
            }
        }

        fun pauseCarousel() {
            autoScrollJob?.cancel()
            autoScrollJob = null
        }

        fun playCarousel() {
            isAutoScrollable = true
            autoScrollJob = Job()
            CoroutineScope(autoScrollJob!! + Dispatchers.Unconfined).launch {
                while (true) {
                    delay(attributes.pageChangeDuration)
                    scrollToNext()
                }
            }
        }

        fun setAdapter(carouselAdapter: CarouselAdapter<*, *>) {
            recyclerViewAdapter = carouselAdapter
            itemDecoration = CirclePagerIndicatorDecoration(carouselAdapter.count, PagerSnapHelper())
            initAttributes(attributes)
            if (attributes.isIndicatorVisible) {
                addItemDecoration(itemDecoration!!)
            }
            adapter = carouselAdapter
        }

        fun showIndicator() {
            attributes.isIndicatorVisible = true
            itemDecoration?.apply {
                addItemDecoration(this)
            }
        }

        fun hideIndicator() {
            attributes.isIndicatorVisible = false
            itemDecoration?.apply {
                removeItemDecoration(this)
            }
        }

        fun setSelectedIndicatorRadius(radius: Float) {
            itemDecoration?.setSelectedIndicatorRadius(radius)
        }

        fun setUnselectedIndicatorRadius(radius: Float) {
            itemDecoration?.setUnelectedIndicatorRadius(radius)
        }

        fun setSelectedIndicatorColor(
            @ColorInt color: Int,
        ) {
            itemDecoration?.setSelectedIndicatorColor(color)
        }

        fun setUnselectedIndicatorColor(
            @ColorInt color: Int,
        ) {
            itemDecoration?.setUnselectedIndicatorColor(color)
        }

        fun setCustomItemDecoration(itemDecoration: ItemDecoration) {
            addItemDecoration(itemDecoration)
        }

        fun setOnCarouselItemClickListener(onCarouselClickListener: OnCarouselClickListener) {
            recyclerViewAdapter.setOnItemClickListener(onCarouselClickListener)
        }

        /**
         * Sets the [OnCarouselTouchListener] which can be used to provide touch delegate from [CarouselRecyclerView] from library to application.
         * @param onCarouselTouchListener : Initializes the touch object.
         * */
        fun setCarouselTouchListener(onCarouselTouchListener: OnCarouselTouchListener) {
            this.onCarouselTouchListener = onCarouselTouchListener
        }

        interface OnCarouselClickListener {
            fun onClickItem(pos: Int)
        }
    }
