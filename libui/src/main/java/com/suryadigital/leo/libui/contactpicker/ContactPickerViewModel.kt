package com.suryadigital.leo.libui.contactpicker

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.ContactsContract
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async

data class Contact(
    val name: String = "",
    val contactNumber: String = "",
    val imageUri: String? = null,
    var isSelected: Boolean = false,
)

/**
 * This [ContactPickerViewModel] will be used to share the data from contact picker.
 * */
class ContactPickerViewModel : ViewModel() {
    private val selectedContact: MutableLiveData<Contact> = MutableLiveData(Contact())
    private val selectedContactList: MutableLiveData<List<Contact>> =
        MutableLiveData(mutableListOf())
    internal var isMultiSelectionModeEnabled: Boolean = false

    private val selectedContactCount: MutableLiveData<Int> = MutableLiveData(0)

    /**
     * This method provides live data on selected [Contact] that can be observed to get the
     * selected contact details in single selection mode.
     * */
    fun getContactNumberLiveData(): LiveData<Contact> = selectedContact

    /**
     * This method provides live data on [List<Contact>] that can be observed to get the
     * list of contact selected in multi-selection mode.
     * */
    fun getSelectedContactsLiveData(): LiveData<List<Contact>> = selectedContactList

    /**
     * This method provides live data on selected count.
     * */
    fun getSelectedContactCountLiveData(): LiveData<Int> = selectedContactCount

    /**
     * This method sets the selected contact count.
     * @param selectedContactCount: Count of selected contact.
     * */
    fun updateSelectedContactCountLiveData(selectedContactCount: Int) {
        this.selectedContactCount.value = selectedContactCount
    }

    internal fun updateContact(selectedContact: Contact) {
        this.selectedContact.value = selectedContact
    }

    internal fun updateSelectedContactList(contacts: MutableList<Contact>) {
        selectedContactList.value = contacts
    }

    /**
     * This method will clear the [ContactPickerViewModel] for both single selection as well as multi-selection mode.
     * */
    fun reset() {
        selectedContact.value = Contact()
        selectedContactList.value = mutableListOf()
    }

    internal suspend fun fetchContacts(context: Context): ArrayList<Contact> {
        val fetchedContactList = ArrayList<Contact>()
        var finalContactListResult: Deferred<ArrayList<Contact>> = CompletableDeferred()
        val phoneContentUri: Uri = ContactsContract.CommonDataKinds.Phone.CONTENT_URI

        val projection =
            arrayOf(
                ContactsContract.CommonDataKinds.Phone.NUMBER,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                ContactsContract.CommonDataKinds.Phone.PHOTO_THUMBNAIL_URI,
                ContactsContract.Contacts._ID,
            )
        val cursor: Cursor? =
            context.contentResolver.query(
                phoneContentUri,
                projection,
                null,
                null,
                "${ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME} ASC",
            )

        cursor?.apply {
            val nameIndex = getColumnIndex(ContactsContract.Contacts.DISPLAY_NAME)
            val phoneIndex = getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
            val photoUriIndex =
                getColumnIndex(ContactsContract.CommonDataKinds.Phone.PHOTO_THUMBNAIL_URI)

            finalContactListResult =
                viewModelScope.async {
                    var lastNumber = ""
                    while (cursor.moveToNext()) {
                        val info =
                            Contact(
                                contactNumber = getString(phoneIndex),
                                name = getString(nameIndex),
                                imageUri = getString(photoUriIndex),
                            )
                        // Thanks: https://stackoverflow.com/questions/32069014/android-get-phone-contacts-and-remove-duplicates
                        if (lastNumber.removeBlankSpaces() != info.contactNumber.removeBlankSpaces()) {
                            fetchedContactList.add(info)
                            lastNumber = info.contactNumber
                        }
                    }
                    fetchedContactList
                }
        }
        cursor?.close()
        return finalContactListResult.await()
    }

    /**
     * An extension function which removes all white spaces from given string.
     * Thanks: https://www.baeldung.com/kotlin/string-remove-whitespace
     * @return: A string with no white spaces in it.
     * */
    private fun String.removeBlankSpaces(): String = filterNot(Char::isWhitespace)
}
