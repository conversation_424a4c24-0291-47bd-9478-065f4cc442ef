package com.suryadigital.leo.libui.verticalimagebutton

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.graphics.drawable.RippleDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.res.ResourcesCompat
import com.suryadigital.leo.libui.R
import com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButtonAttributes.Companion.DEFAULT_LAYOUT_WEIGHT

/**
 * Implements a custom view with a vertical arrangement of an icon and a text, which can be used as
 * a button/ clickable view.
 *
 * @attribute labelText: Text that would be displayed below the view icon.
 * @attribute labelTextSize: Size of the the `labelText`, preferred in `SP`
 * @attribute iconSource: Drawable that would be displayed as the icon.
 * @attribute iconLabelSeparation: Signifies the separation between the icon and the label text,F
 * note: Accepts negative values and the icon and label will move close for negative values.
 * @attribute iconBackgroundCornerRadius: Radius of the icon background corner, note: Making this
 * half of the width of the view's (VerticalImageButton's) width would yield in round background for
 * the icons.
 * @attribute iconBackgroundColor: Color of the icon background, note: Making this transparent would not
 * show any background for the icon in the view.
 * @attribute iconHeightPercent: This signifies the ratio of the total view's height that which
 * the icon occupies, the values allowed are between 0 - 1, invalid inputs will revert the percent
 * to default value.
 * @attribute labelTextFontFamily: Sets the font family of the label text-view, it can be nullable,
 * if it is null, font family won't be updated.
 * @attribute labelTextColor: Sets the color of the label text.
 * @attribute labelTextFontStyle: Sets the font style to [Typeface.NORMAL], [Typeface.BOLD],
 * [Typeface.ITALIC] or [Typeface.BOLD_ITALIC].
 * Note: [Typeface.NORMAL] is considered as the default value.
 *
 * @property attributes: Holds the attributes asociating with the VerticalImageButton
 */
class VerticalImageButton(
    context: Context,
    attributeSet: AttributeSet? = null,
) : LinearLayout(context, attributeSet) {
    companion object {
        @JvmStatic
        private val LAYOUT_WEIGHT_SCALING_FACTOR = 10
    }

    lateinit var iconImageView: ImageView private set
    lateinit var labelTextView: TextView private set
    lateinit var iconBackgroundCardView: CardView private set
    private lateinit var wrapperLinearLayout: LinearLayout

    val attributes: VerticalImageButtonAttributes

    init {
        inflate(context, R.layout.vertical_image_button, this)
        attributes = VerticalImageButtonAttributes(context, attributeSet)

        initComponents()
        setIcon(attributes.iconSource, attributes.iconPadding)
        setButtonLabel(
            attributes.labelText,
            attributes.labelTextSize,
            attributes.labelTextFontFamily,
            attributes.labelTextColor,
            attributes.labelTextFontStyle,
        )
        setIconBackground(attributes.iconBackgroundCornerRadius, attributes.iconBackgroundColor)
        setLayoutParams(
            attributes.iconHeightPercent,
            attributes.iconLabelSeparation,
            attributes.iconWidth,
        )
        setBackgroundRipple(attributes.rippleRadius)
    }

    private fun initComponents() {
        iconImageView = findViewById(R.id.icon)
        labelTextView = findViewById(R.id.button_label)
        iconBackgroundCardView = findViewById(R.id.icon_background_card)
        wrapperLinearLayout = findViewById(R.id.vertical_image_button_wrapper)
    }

    /**
     * This method allows to set the background parameters for the icon.
     *
     * @param iconBackgroundColor: Color of the icon background, make this transparent to just show
     * the icon without any background.
     * @param iconBackgroundCornerRadius: Radius of the background card-view that holds the icon,
     * make this equal to the half of width of the element [VerticalImageButton] to have a round background.
     */
    fun setIconBackground(
        iconBackgroundCornerRadius: Float,
        iconBackgroundColor: Int,
    ) {
        attributes.iconBackgroundColor = iconBackgroundColor
        attributes.iconBackgroundCornerRadius = iconBackgroundCornerRadius

        if (iconBackgroundColor == Color.TRANSPARENT) {
            iconBackgroundCardView.background =
                ResourcesCompat.getDrawable(resources, R.drawable.vertical_image_button_background, null)
        } else {
            iconBackgroundCardView.setCardBackgroundColor(iconBackgroundColor)
        }
        iconBackgroundCardView.radius = iconBackgroundCornerRadius
    }

    /**
     * Sets the layout parameters corresponding to the view [VerticalImageButton].
     *
     * @param iconHeightPercent: This signifies the ratio of the total view's height that which
     * the icon occupies, the values allowed are between 0 - 1, invalid inputs will revert the percent
     * to default value.
     * @param iconLabelSeparation: Signifies the separation between the icon and the label text,
     * note: Accepts negative values and the icon and label will move close for negative values.
     * @param iconWidth: width of the icon image,
     *     *. [iconWidth] includes the [iconPadding].
     *     *. when [iconWidth] is `-1` actual width will be considered as `MATCH PARENT`.
     */
    fun setLayoutParams(
        iconHeightPercent: Float,
        iconLabelSeparation: Float,
        iconWidth: Float,
    ) {
        var iconLayoutWeight = DEFAULT_LAYOUT_WEIGHT
        var labelLayoutWeight = DEFAULT_LAYOUT_WEIGHT

        if (iconHeightPercent < 1 && iconHeightPercent > 0) {
            iconLayoutWeight = (iconHeightPercent * LAYOUT_WEIGHT_SCALING_FACTOR)
            labelLayoutWeight =
                LAYOUT_WEIGHT_SCALING_FACTOR - (iconHeightPercent * LAYOUT_WEIGHT_SCALING_FACTOR)
        }

        attributes.iconHeightPercent = iconHeightPercent
        attributes.iconLabelSeparation = iconLabelSeparation
        attributes.iconWidth = iconWidth

        val iconWidthApprox = iconWidth.toInt()
        iconBackgroundCardView.layoutParams =
            getLinearLayoutParams(iconLayoutWeight, iconWidthApprox)
        labelTextView.layoutParams =
            getLinearLayoutParams(labelLayoutWeight, LayoutParams.MATCH_PARENT, iconLabelSeparation)
    }

    /**
     * Sets the background ripple to the button of specified radius.
     * @param radius : Radius of the ripple.
     * */
    fun setBackgroundRipple(radius: Int) {
        attributes.rippleRadius = radius
        val rippleDrawable =
            ResourcesCompat.getDrawable(
                context.resources,
                R.drawable.button_ripple_background,
                context.theme,
            ) as RippleDrawable
        rippleDrawable.radius = attributes.rippleRadius
        wrapperLinearLayout.background = rippleDrawable
    }

    private fun getLinearLayoutParams(
        layoutWeight: Float,
        layoutWidth: Int = LayoutParams.MATCH_PARENT,
        topMargin: Float? = null,
    ): LayoutParams {
        val layoutParams =
            LayoutParams(
                layoutWidth,
                0,
                layoutWeight,
            )

        topMargin?.let {
            layoutParams.topMargin = topMargin.toInt()
        }

        return layoutParams
    }

    /**
     * Sets the button label text parameters.
     *
     * @param labelText: Sets the text that would be displayed beneath the icon.
     * @param labelTextSize: Specifies the size of the label text, this should be specified in
     * `pixels`.
     * @param labelTextFontFamily: Sets the font family of the label text-view, it can be nullable,
     * if it is null, font family won't be updated.
     * @param labelTextColor: Sets the color of the label text.
     * @param labelTextFontStyle: Sets the font style to [Typeface.NORMAL], [Typeface.BOLD],
     * [Typeface.ITALIC] or [Typeface.BOLD_ITALIC].
     */
    fun setButtonLabel(
        labelText: String,
        labelTextSize: Int,
        labelTextFontFamily: Typeface?,
        labelTextColor: Int,
        labelTextFontStyle: Int,
    ) {
        attributes.labelText = labelText
        attributes.labelTextSize = labelTextSize
        attributes.labelTextColor = labelTextColor
        attributes.labelTextFontStyle = labelTextFontStyle

        if (labelTextFontFamily != null) {
            attributes.labelTextFontFamily = labelTextFontFamily
        }

        labelTextView.text = labelText
        labelTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, labelTextSize.toFloat())
        labelTextView.setTextColor(labelTextColor)
        labelTextView.setTypeface(labelTextFontFamily, labelTextFontStyle)
    }

    /**
     * Sets the icon related attributes in the  in the view.
     *
     * @param iconSource: Drawable that will be shown as the icon, this can be null and when it is
     * null, the icon won't get updated.
     * @param iconPadding: This signifies the padding between the icon and the icon background.
     * Note: This won't clip the icon, instead it will be scaled accordingly.
     */
    fun setIcon(
        iconSource: Drawable?,
        iconPadding: Float,
    ) {
        attributes.iconPadding = iconPadding

        if (iconSource != null) {
            attributes.iconSource = iconSource
            iconImageView.setImageDrawable(iconSource)
        }
        iconImageView.layoutParams = getFrameLayoutParams(iconPadding)
    }

    private fun getFrameLayoutParams(iconPadding: Float): FrameLayout.LayoutParams {
        val layoutParams =
            FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT,
            )

        val iconPaddingApprox = iconPadding.toInt()
        layoutParams.setMargins(
            iconPaddingApprox,
            iconPaddingApprox,
            iconPaddingApprox,
            iconPaddingApprox,
        )

        return layoutParams
    }

    override fun setOnClickListener(listener: OnClickListener?) {
        wrapperLinearLayout.setOnClickListener(listener)
        super.setOnClickListener(listener)
    }
}
