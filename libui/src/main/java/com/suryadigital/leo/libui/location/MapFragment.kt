package com.suryadigital.leo.libui.location

import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ImageView
import androidx.core.graphics.createBitmap
import androidx.core.graphics.drawable.toBitmap
import androidx.fragment.app.Fragment
import coil.Coil
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.GoogleMap.OnCameraMoveStartedListener.REASON_GESTURE
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.android.gms.maps.model.MapStyleOptions
import com.google.android.gms.maps.model.Marker
import com.google.android.gms.maps.model.MarkerOptions
import com.suryadigital.leo.libui.R
import com.suryadigital.leo.libui.location.model.BitmapSource
import com.suryadigital.leo.libui.location.model.BoundingCoordinates
import com.suryadigital.leo.libui.location.model.DrawableSource
import com.suryadigital.leo.libui.location.model.FileSource
import com.suryadigital.leo.libui.location.model.HttpUrlSource
import com.suryadigital.leo.libui.location.model.IntSource
import com.suryadigital.leo.libui.location.model.LatitudeLongitudeBounds
import com.suryadigital.leo.libui.location.model.Location
import com.suryadigital.leo.libui.location.model.LocationMarkerSource
import com.suryadigital.leo.libui.location.model.StringSource
import com.suryadigital.leo.libui.location.model.UriSource

private const val TAG = "MapFragment"

class MapFragment : Fragment(R.layout.layout_map) {
    private lateinit var googleMap: GoogleMap
    private lateinit var locationsList: List<Location>
    private val listOfMarkers = arrayListOf<Marker>()
    private var onMarkerClickCallback: OnMarkerClickListener? = null
    private var onMapClickCallback: OnMapClickListener? = null
    private var onCameraMoveCallback: OnCameraMoveListener? = null
    private var imageLoadListener: ImageLoadListener? = null
    private var defaultMarkerImage: LocationMarkerSource = IntSource(R.drawable.ic_location_defult)
    private var selectedMarker: Marker? = null
    private var selectedLocation: Location? = null
    private var isMarkerHighlightingEnabled = true
    private var isCameraMovedByUserGesture = false

    // By default we want camera to pan to bound all locations on map.
    private var cameraPanEnabled = true

    /**
     * To receive click events on a marker, you must register to [OnMarkerClickListener].
     */
    interface OnMarkerClickListener {
        /**
         * Called when user clicks on a marker.
         */
        fun onMarkerClick(index: Int)
    }

    /**
     * To receive click events on map, you must register to [OnMapClickListener].
     */
    interface OnMapClickListener {
        /**
         * Called when user taps any where on map except a marker.
         */
        fun onMapClick()
    }

    /**
     * To receive map drag events, you must register to [OnCameraMoveListener].
     */
    interface OnCameraMoveListener {
        /**
         * Called when user drags map.
         */
        fun onMapDragged()
    }

    /**
     * To receive image loading error events, you must register to [ImageLoadListener].
     */
    interface ImageLoadListener {
        /**
         * Called when there is an error occurred while loading the marker image.
         */
        fun onImageLoadError(drawable: Drawable?)
    }

    /**
     * Sets the locations which will be shown on map.
     *
     * NOTE: The map will only display the locations in the [locationsList]. All other locations will
     * be reset.
     */
    fun setLocations(
        locationsList: List<Location>,
        shouldPan: Boolean,
    ) {
        this.locationsList = locationsList
        cameraPanEnabled = shouldPan

        /*
            Removing all previous markers because map will re draw the marker on the previous
            ones if you have passed old locations as well in the list.
         */
        listOfMarkers.forEach(Marker::remove)

        listOfMarkers.clear()

        /*
            If map is already initialized and then you set the list of locations then we have
            set the marker here.
         */
        if (::googleMap.isInitialized) {
            setMarkersAndMoveCamera()
        }
    }

    /**
     * Sets the bound to the given lat long with the zoom level specified.
     *
     * @param: [location] - location for which the marker is to be set
     * @param: [zoomLevel] - this is used to set the scale of the map.
     *
     */
    fun setBoundWithMarkerAndZoomLevel(
        location: Location,
        zoomLevel: ZoomLevel,
    ) {
        createMarker(location)
        if (::googleMap.isInitialized) {
            googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(LatLng(location.lat, location.long), zoomLevel.value))
        }
    }

    enum class ZoomLevel(
        val value: Float,
    ) {
        WORLD(1f),
        LANDMASS_CONTINENT(5f),
        CITY(10f),
        STREETS(15f),
        BUILDINGS(20f),
    }

    /**
     * Sets the bound to the given lat long with the zoom level specified.
     *
     * @param: [startLocation] - location from which distance needs to be calculated
     * @param: [endLocation] - location till which distance needs to be calculated
     *
     * @return: Distance between 2 location in meters.
     */
    fun findDistanceBetweenPoints(
        startLocation: Location,
        endLocation: Location,
    ): Float {
        val results = FloatArray(1)
        android.location.Location.distanceBetween(startLocation.lat, startLocation.long, endLocation.lat, endLocation.long, results)
        return results[0]
    }

    /**
     * Sets default Marker Image.
     *
     * [defaultMarkerImage] is initialized with lib default image and if not set by you, then
     * same will be used as default, otherwise your image will be used.
     */
    fun setDefaultMarkerImage(defaultImage: LocationMarkerSource) {
        defaultMarkerImage = defaultImage

        /*
            Reset all markers when default image is set.
         */
        if (::googleMap.isInitialized && ::locationsList.isInitialized) {
            setMarkersAndMoveCamera()
        }
    }

    /**
     * Registers [OnMarkerClickListener].
     */
    fun setOnMarkerClickListener(onMarkerClickCallback: OnMarkerClickListener) {
        this.onMarkerClickCallback = onMarkerClickCallback
    }

    /**
     * Registers [OnMapClickListener].
     */
    fun setOnMapClickListener(onMapClickCallback: OnMapClickListener) {
        this.onMapClickCallback = onMapClickCallback
    }

    /**
     * Registers [OnCameraMoveListener].
     */
    fun setOnCameraMoveListener(onCameraMoveCallback: OnCameraMoveListener) {
        this.onCameraMoveCallback = onCameraMoveCallback
    }

    /**
     * Registers [ImageLoadListener].
     */
    fun setOnImageLoadListener(imageLoadListener: ImageLoadListener) {
        this.imageLoadListener = imageLoadListener
    }

    /**
     * Enable or Disable the highlighting of marker when user taps on it.
     *
     * If enabled, it will highlight the selected marker on map.
     * If disabled, it will un-highlight the selected marker on map.
     */
    fun enableMapMarkerHighlighting(enable: Boolean) {
        isMarkerHighlightingEnabled = enable
        /*
            If a marker is already highlighted and later a caller disables this feature
            then we need to unhighlight previously highlighted marker (if any). The opposite
            holds as well.
         */
        highlightSelectedMarker(enable)
    }

    /**
     * Gives top left and bottom right coordinates of visible region on map.
     */
    fun getVisibileRegionBounds(): LatitudeLongitudeBounds {
        val northeast = googleMap.projection.visibleRegion.latLngBounds.northeast
        val southwest = googleMap.projection.visibleRegion.latLngBounds.southwest

        val topLeft = BoundingCoordinates(southwest.latitude, northeast.longitude)
        val bottomRight = BoundingCoordinates(northeast.latitude, southwest.longitude)

        return LatitudeLongitudeBounds(topLeft, bottomRight)
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)

        val mapFragment = childFragmentManager.findFragmentById(R.id.mapFragment) as SupportMapFragment
        mapFragment.getMapAsync(onMapReadyCallback)
    }

    override fun onResume() {
        super.onResume()

        if (::googleMap.isInitialized) {
            mapStyling()
        }
    }

    private val onMapReadyCallback =
        OnMapReadyCallback { googleMap ->
            <EMAIL> = googleMap
            <EMAIL>(onMarkerClickListener)
            <EMAIL>(onMapClickListener)
            <EMAIL>(onCameraMoveStartedListener)
            <EMAIL>(onCameraIdleListener)

            mapStyling()

            setMarkersAndMoveCamera()
        }

    private fun updateOrResetSelectedMarker(isSelected: Boolean) {
        if (isMarkerHighlightingEnabled) {
            highlightSelectedMarker(isSelected)
        } else {
            Log.i(TAG, "Marker highlighting is not enabled.")
        }
    }

    private fun highlightSelectedMarker(isEnabled: Boolean) {
        selectedMarker?.let { nonNullMarker ->
            selectedLocation?.let { nonNullLocation ->
                requestCoilAndUpdateMarker(nonNullMarker, nonNullLocation.thumbnail ?: defaultMarkerImage, isEnabled)
            }
        }
    }

    private val onMarkerClickListener =
        GoogleMap.OnMarkerClickListener { selectedMarker ->
            // Resetting previous selected marker.
            updateOrResetSelectedMarker(false)
            this.selectedMarker = selectedMarker
            selectedLocation =
                locationsList.findLast {
                    it.lat == selectedMarker.position.latitude &&
                        it.long == selectedMarker.position.longitude
                }
            // Updating (highlighting) the latest selected marker.
            updateOrResetSelectedMarker(true)
            onMarkerClickCallback?.onMarkerClick(locationsList.indexOf(selectedLocation))
            true
        }

    private val onMapClickListener =
        GoogleMap.OnMapClickListener {
            // Resetting latest selected marker when user taps on map
            updateOrResetSelectedMarker(false)
            onMapClickCallback?.onMapClick()
            selectedLocation = null
            selectedMarker = null
        }

    private val onCameraMoveStartedListener =
        GoogleMap.OnCameraMoveStartedListener { reason ->
            if (reason == REASON_GESTURE) {
                isCameraMovedByUserGesture = true
            } else {
                Log.i("GoogleMap", "Camera not moved by user's gesture")
            }
        }

    private val onCameraIdleListener =
        GoogleMap.OnCameraIdleListener {
            if (isCameraMovedByUserGesture) {
                onCameraMoveCallback?.onMapDragged()
                isCameraMovedByUserGesture = false
            } else {
                Log.i("GoogleMap", "Camera not moved by user's gesture")
            }
        }

    private fun setMarkersAndMoveCamera() {
        if (::locationsList.isInitialized) {
            val latLongBuilder = LatLngBounds.builder()
            locationsList.forEach { location ->
                latLongBuilder.include(LatLng(location.lat, location.long))
                createMarker(location)
            }
            val bounds = latLongBuilder.build()
            if (cameraPanEnabled) {
                googleMap.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, 100))
            }
        }
    }

    private fun mapStyling() {
        val styleResource =
            when (requireContext().resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK) {
                Configuration.UI_MODE_NIGHT_YES -> R.raw.map_dark_mode
                Configuration.UI_MODE_NIGHT_NO, Configuration.UI_MODE_NIGHT_UNDEFINED -> R.raw.map_light_mode
                else -> R.raw.map_light_mode
            }

        try {
            // Customise the styling of the base map using a JSON object defined
            // in a raw resource file.
            googleMap.setMapStyle(
                MapStyleOptions.loadRawResourceStyle(requireContext(), styleResource),
            )
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "Can't find style. Error: ", e)
        }
    }

    private fun getImageSource(thumbnail: LocationMarkerSource): Any =
        when (thumbnail) {
            is StringSource -> thumbnail.source
            is HttpUrlSource -> thumbnail.source
            is UriSource -> thumbnail.source
            is FileSource -> thumbnail.source
            is IntSource -> thumbnail.source
            is DrawableSource -> thumbnail.source
            is BitmapSource -> thumbnail.source
        }

    private fun createMarker(location: Location) {
        val imageSource = location.thumbnail ?: defaultMarkerImage

        requestCoilAndSetMarker(location.title, location.lat, location.long, imageSource)
    }

    private fun requestCoilAndUpdateMarker(
        selectedMarker: Marker,
        thumbnail: LocationMarkerSource,
        isSelected: Boolean,
    ) {
        val request =
            ImageRequest
                .Builder(requireContext())
                .data(getImageSource(thumbnail))
                .lifecycle(viewLifecycleOwner)
                .allowHardware(false)
                .target(
                    onSuccess = { result ->
                        selectedMarker.setIcon(BitmapDescriptorFactory.fromBitmap(generateMarkerBitmap(result.toBitmap(), isSelected)))
                    },
                    onError = {
                        imageLoadListener?.onImageLoadError(it)
                    },
                ).memoryCachePolicy(CachePolicy.ENABLED)
                .build()

        executeCoilRequest(request)
    }

    private fun requestCoilAndSetMarker(
        title: String,
        lat: Double,
        long: Double,
        thumbnail: LocationMarkerSource,
    ) {
        val request =
            ImageRequest
                .Builder(requireContext())
                .data(getImageSource(thumbnail))
                .lifecycle(viewLifecycleOwner)
                .allowHardware(false)
                .target(
                    onSuccess = { result ->
                        addMarker(createMarkerOption(title, lat, long, generateMarkerBitmap(result.toBitmap(), false)))
                    },
                    onError = {
                        addMarker(MarkerOptions().position(LatLng(lat, long)).title(title))
                        imageLoadListener?.onImageLoadError(it)
                    },
                ).memoryCachePolicy(CachePolicy.ENABLED)
                .build()

        executeCoilRequest(request)
    }

    private fun executeCoilRequest(request: ImageRequest) {
        Coil.imageLoader(requireContext()).enqueue(request)
    }

    private fun generateMarkerBitmap(
        thumbnail: Bitmap,
        isSelected: Boolean,
    ): Bitmap {
        val layout = if (isSelected) R.layout.map_marker_selected else R.layout.map_marker
        val marker = View.inflate(requireContext(), layout, null)
        val markerImage = marker.findViewById<ImageView>(R.id.markerImage)

        markerImage.setImageBitmap(thumbnail)

        marker.measure(
            View.MeasureSpec.UNSPECIFIED,
            View.MeasureSpec.UNSPECIFIED,
        )
        marker.layout(0, 0, marker.measuredWidth, marker.measuredHeight)
        val generatedBitmap = createBitmap(marker.measuredWidth, marker.measuredHeight)
        val canvas = Canvas(generatedBitmap)
        marker.draw(canvas)

        return generatedBitmap
    }

    private fun createMarkerOption(
        title: String,
        lat: Double,
        long: Double,
        thumbnail: Bitmap,
    ) = MarkerOptions()
        .position(LatLng(lat, long))
        .title(title)
        .icon(BitmapDescriptorFactory.fromBitmap(thumbnail))

    private fun addMarker(markerOptions: MarkerOptions) {
        val marker = googleMap.addMarker(markerOptions)
        if (marker !== null) {
            listOfMarkers.add(marker)
        } else {
            Log.e(TAG, "Failed to add marker with options: $markerOptions")
        }
    }
}
