package com.suryadigital.leo.libui.qrcode

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.util.Size
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import com.suryadigital.leo.libui.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.Executors

class BarcodeFragment : Fragment(R.layout.fragment_barcode) {
    private lateinit var previewView: PreviewView
    private lateinit var camera: Camera
    private lateinit var cameraProvider: ProcessCameraProvider
    private var barcodeListener: BarcodeListener? = null
    private var scanFormat: Int = ScanFormat.ALL_FORMAT.value
    private var previewSize: PreviewSize = PreviewSize(1080, 768)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply {
            scanFormat = getInt(SCAN_FORMAT)
        }
    }

    private fun initViews() {
        previewView = view?.findViewById(R.id.previewView)
            ?: throw IllegalStateException("init() function shouldn't be called before onCreateView")
    }

    /**
     * Once you have camera permission call this function to start the barcode scanning.
     * */
    fun startBarcodeDetection() {
        if (checkCameraPermissionGranted(requireContext())) {
            initViews()
            val cameraProviderFuture = ProcessCameraProvider.getInstance(requireContext())

            cameraProviderFuture.addListener({
                cameraProvider = cameraProviderFuture.get()

                val previewUseCase =
                    Preview
                        .Builder()
                        .build()
                        .also {
                            it.setSurfaceProvider(previewView.surfaceProvider)
                        }

                val options =
                    BarcodeScannerOptions
                        .Builder()
                        .setBarcodeFormats(
                            Barcode.FORMAT_QR_CODE,
                            Barcode.FORMAT_ALL_FORMATS,
                            Barcode.FORMAT_DATA_MATRIX,
                            Barcode.TYPE_PHONE,
                            Barcode.TYPE_EMAIL,
                            Barcode.TYPE_PRODUCT,
                        ).build()

                val scanner = BarcodeScanning.getClient(options)

                val analysisUseCase =
                    ImageAnalysis
                        .Builder()
                        .build()

                analysisUseCase.setAnalyzer(
                    Executors.newSingleThreadExecutor(),
                    // Can't use a lambda here because we need to override a newly added method in ImageAnalysis.Analyzer.
                    // On newer CameraX versions, getDefaultTargetResolution() has a default implementation,
                    // but on older devices (API 23) it isn’t present and causes:
                    // `java.lang.AbstractMethodError: abstract method "android.util.Size androidx.camera.core.ImageAnalysis$Analyzer.getDefaultTargetResolution()"`
                    // To avoid this runtime crash, we use an anonymous object and manually override getDefaultTargetResolution() to return `null`.
                    // Using a lambda here works for devices with API 25+.
                    object : ImageAnalysis.Analyzer {
                        override fun analyze(imageProxy: ImageProxy) {
                            processImageProxy(scanner, imageProxy)
                            imageProxy.close()
                        }

                        override fun getDefaultTargetResolution(): Size? = null
                    },
                )

                val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                try {
                    cameraProvider.unbindAll()
                    camera =
                        cameraProvider.bindToLifecycle(
                            this,
                            cameraSelector,
                            previewUseCase,
                            analysisUseCase,
                        )
                } catch (exc: Exception) {
                    Log.e(TAG, "Use case binding failed", exc)
                }
            }, ContextCompat.getMainExecutor(requireContext()))
        }
    }

    /**
     * This function is used to pass image to MLkit "process" method, which decodes the barcode.
     * The getImage() method makes and assumption that each ImageProxy is sole owner of underlying
     * android.media.Image which is true in our case, that's why we have to use "UnsafeOptInUsageError".
     * Read more : https://stackoverflow.com/questions/69899249/whats-the-unsafeoptinusageerror
     * */
    @SuppressLint("UnsafeOptInUsageError")
    private fun processImageProxy(
        barcodeScanner: BarcodeScanner,
        imageProxy: ImageProxy,
    ) {
        imageProxy.image?.let { image ->
            val inputImage =
                InputImage.fromMediaImage(
                    image,
                    imageProxy.imageInfo.rotationDegrees,
                )

            barcodeScanner
                .process(inputImage)
                .addOnSuccessListener { barcodeList ->
                    val barcode = barcodeList.getOrNull(0)

                    // `rawValue` is the decoded value of the barcode
                    barcode?.rawValue?.let { value ->
                        if (value.isNotEmpty()) {
                            CoroutineScope(Dispatchers.Main).launch {
                                barcodeListener?.onBarcodeValueReceived(value)
                            }
                        }
                    }
                }.addOnFailureListener {
                    /* This failure will happen if the barcode scanning model fails to download from
                    Google Play Services*/

                    Log.e(TAG, it.message.orEmpty())
                }.addOnCompleteListener {
                    /* When the image is from CameraX analysis use case, must call image.close() on
                     received images when finished using them. Otherwise, new images may not be
                     received or the camera may stall. */

                    imageProxy.image?.close()
                    imageProxy.close()
                }
        }
    }

    fun toggleFlashLight(turnOnFlash: Boolean) {
        if (camera.cameraInfo.hasFlashUnit()) {
            camera.cameraControl.enableTorch(turnOnFlash)
        }
    }

    /**
     * This method will help to stop the barcode detection on runtime, and also to configure single item scanning.
     * */
    fun stopBarcodeDetection() {
        if (checkCameraPermissionGranted(requireContext())) {
            cameraProvider.unbindAll()
        }
    }

    /**
     * This method sets the preview size of camera source.
     * @param height : Sets the height.
     * @param width : Sets the width.
     * */
    fun setPreviewSize(
        height: Int,
        width: Int,
    ) {
        previewSize = PreviewSize(height, width)
    }

    /**
     * This method sets the barcode listener to recieve output and error callbacks.
     * @param barcodeListener : barcode listener which handle output value and error callbacks.
     * */
    fun setBarcodeListener(barcodeListener: BarcodeListener) {
        this.barcodeListener = barcodeListener
    }

    companion object {
        const val SCAN_FORMAT: String = "scan_format"
        private val TAG = BarcodeFragment::class.java.simpleName
    }
}

data class PreviewSize(
    val height: Int,
    val width: Int,
)

/**
 * Provides function to set the scan format for scanner.
 * */
fun BarcodeFragment.setScanFormat(scanFormat: ScanFormat) {
    val bundle = Bundle()
    bundle.putInt(BarcodeFragment.SCAN_FORMAT, scanFormat.value)
    this.arguments = bundle
}

/**
 * This function helps to check if we have camera permission.
 * @param context : context used by [ContextCompat] to check the permission.
 * */
fun checkCameraPermissionGranted(context: Context): Boolean =
    ContextCompat.checkSelfPermission(
        context,
        Manifest.permission.CAMERA,
    ) == PackageManager.PERMISSION_GRANTED
