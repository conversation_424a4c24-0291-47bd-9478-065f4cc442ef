package com.suryadigital.leo.libui.progressbutton

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import com.suryadigital.leo.libui.R

/**
 * Implements the [ProgressButton], a button with progress bar at center. Once user taps the button it will disable the button
 * click and start the progress bar. Once the action is completed call [hideProgressBar] and the button will toggle back with
 * the text being visible, and hide the progress bar.
 * */
class ProgressButton(context: Context, attributeSet: AttributeSet? = null) :
    FrameLayout(context, attributeSet) {

    internal val attributes: ProgressButtonAttributes
    private var progressTV: TextView
    private var progressBar: ProgressBar
    private var rootView: CardView

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_progress_button, this, true)
        progressTV = findViewById(R.id.progress_tv)
        progressBar = findViewById(R.id.progress_bar)
        rootView = findViewById(R.id.progress_button_root_cv)
        attributes = ProgressButtonAttributes(context, attributeSet)
        progressTV.text = attributes.progressButtonText
        rootView.backgroundTintList =
            ColorStateList.valueOf(attributes.progressButtonBackgroundColor)
        rootView.radius = attributes.progressButtonRadius
        rootView.elevation = attributes.progressButtonElevation
        progressTV.setTextColor(attributes.progressButtonTextColor)
        progressTV.setTypeface(progressTV.typeface, attributes.buttonTextFontStyle)
        progressBar.indeterminateTintList =
            ColorStateList.valueOf(attributes.progressButtonTextColor)
        with(TypedValue()) {
            context.theme.resolveAttribute(android.R.attr.selectableItemBackground, this, true)
            rootView.foreground = ContextCompat.getDrawable(context, resourceId)
        }
    }

    /**
     * This method will start the progress bar and hide button text and disable the button click.
     * */
    fun showProgressBar() {
        progressBar.visibility = View.VISIBLE
        progressTV.visibility = View.GONE
        rootView.isClickable = true
        rootView.isEnabled = true
    }

    /**
     * This method will hide the progress bar and show the button text and enable button click.
     * */
    fun hideProgressBar() {
        rootView.isClickable = false
        rootView.isEnabled = false
        progressBar.visibility = View.INVISIBLE
        progressTV.visibility = View.VISIBLE
    }

    /**
     * This method will set the button text.
     * @param text : Sets the text to button.
     * */
    fun setText(text: String) {
        progressTV.text = text
    }

    /**
     * This method will set the text color.
     * @param color : Sets the color int value.
     * */
    fun setTextColor(@ColorInt color: Int) {
        progressTV.setTextColor(color)
        progressBar.indeterminateTintList = ColorStateList.valueOf(color)
    }

    /**
     * This method will set the button background tint color.
     * @param color : The color for the background tint of the button.
     * */
    override fun setBackgroundColor(@ColorInt color: Int) {
        rootView.backgroundTintList = ColorStateList.valueOf(color)
    }

    /**
     * This method will set the corner radius of button.
     * @param radius : Sets the corner radius of progress button.
     * */
    fun setCornerRadius(radius: Float) {
        rootView.radius = radius
    }

    /**
     * This method will set the typeface of the text view.
     * @param typeface : The typeface to be set for the text view.
     * NORMAL = 0
     * BOLD = 1
     * ITALIC = 2
     * BOLD_ITALIC = 3
     *
     * @sample setTextTypeface(Typeface.BOLD)
     * */
    fun setTextTypeface(typeface: Int) {
        progressTV.setTypeface(null, typeface)
    }

    fun getText(): CharSequence = progressTV.text

    fun getCornerRadius(): Float = rootView.radius
}
