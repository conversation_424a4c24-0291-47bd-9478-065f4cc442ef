package com.suryadigital.leo.libui.viewpager

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import com.suryadigital.leo.libui.R

/**
 * This class handle all the attributes provided through XML layout, it performs initilization of the attributes
 * */
class ViewPagerAttributes(context: Context, attrs: AttributeSet?) {
    var autoScroll: Boolean = false
    var hideIndicator: Boolean = false
    var isPreviewEnabled: Boolean = false
    var selectedIndicatorColor: Int = Color.BLACK
    var scrollerDuration: Long = 1000
    var pageChangeDuration: Long = 3000
    var isStoppedWhenCompleted: Boolean = false

    init {
        val attributes = context.obtainStyledAttributes(attrs, R.styleable.ViewPager, 0, 0)
        try {

            selectedIndicatorColor = attributes.getColor(
                R.styleable.ViewPager_selectedIndicatorColor,
                context.getColor(android.R.color.black),
            )
            isStoppedWhenCompleted =
                attributes.getBoolean(
                    R.styleable.ViewPager_stopScrollingOnComplete,
                    isStoppedWhenCompleted,
                )
            autoScroll = attributes.getBoolean(
                R.styleable.ViewPager_autoScroll,
                autoScroll,
            )
            hideIndicator = attributes.getBoolean(
                R.styleable.ViewPager_hiddenIndicator,
                hideIndicator,
            )
            isPreviewEnabled = attributes.getBoolean(
                R.styleable.ViewPager_previewPages,
                isPreviewEnabled,
            )
            scrollerDuration = attributes.getInt(
                R.styleable.ViewPager_scrollDuration,
                scrollerDuration.toInt(),
            ).toLong()
            pageChangeDuration = attributes.getInt(
                R.styleable.ViewPager_pageChangeDuration,
                pageChangeDuration.toInt(),
            ).toLong()
        } finally {
            attributes.recycle()
        }
    }
}
