package com.suryadigital.leo.libui.qrcode

interface BarcodeListener {
    /**
     * This callback will return the output [rawValue] from barcode detector to the users.
     * @param rawValue : output of the barcode detection.
     * */
    fun onBarcodeValueReceived(rawValue: String)

    /**
     * This one will be called if any error occurs during barcode detections. for eg. [PermissionException] etc.
     * */
    fun onFailure(e: BarcodeException)
}
