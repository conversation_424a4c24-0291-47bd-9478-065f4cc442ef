package com.suryadigital.leo.libui.viewpager

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.animation.Interpolator
import android.widget.Scroller
import androidx.viewpager.widget.ViewPager

internal class ViewPagerContentView : ViewPager {
    private val tag = ViewPagerContentView::class.simpleName
    private var mScroller: ViewPagerScroller? = null

    constructor(context: Context) : super(context) {
        postInitViewPager()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        postInitViewPager()
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean = false

    private fun postInitViewPager() {
        try {
            val viewpager: Class<*> = ViewPager::class.java
            val scroller = viewpager.getDeclaredField("mScroller")
            scroller.isAccessible = true
            val interpolator = viewpager.getDeclaredField("sInterpolator")
            interpolator.isAccessible = true
            mScroller =
                ViewPagerScroller(
                    context,
                    interpolator[null] as Interpolator,
                )
            scroller[this] = mScroller
        } catch (e: Exception) {
            Log.e(tag, "postInitViewPager failed", e)
        }
    }

    fun setPagerScrollDuration(scrollDuration: Int) {
        mScroller?.setScrollDuration(scrollDuration)
    }

    /*
     * Scroller needs to be overriden in order to provide custom scroll duration between pages.
     * */
    inner class ViewPagerScroller : Scroller {
        private var scrollerDuration: Int = 600

        constructor(context: Context?) : super(context)
        constructor(
            context: Context?,
            interpolator: Interpolator?,
        ) : super(context, interpolator)

        fun getScrollDuration(): Int = scrollerDuration

        fun setScrollDuration(mScrollDuration: Int) {
            scrollerDuration = mScrollDuration
        }

        override fun startScroll(
            startX: Int,
            startY: Int,
            dx: Int,
            dy: Int,
            duration: Int,
        ) {
            super.startScroll(startX, startY, dx, dy, scrollerDuration)
        }

        override fun startScroll(
            startX: Int,
            startY: Int,
            dx: Int,
            dy: Int,
        ) {
            super.startScroll(startX, startY, dx, dy, scrollerDuration)
        }
    }
}
