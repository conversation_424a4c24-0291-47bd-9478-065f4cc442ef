package com.suryadigital.leo.libui.passwordfield

import android.content.Context
import android.text.InputType
import android.text.method.PasswordTransformationMethod
import android.util.AttributeSet
import com.google.android.material.textfield.TextInputEditText
import com.suryadigital.leo.libui.R
import java.lang.IllegalArgumentException
import java.util.regex.Pattern

class PasswordTextField : TextInputEditText {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    )

    init {
        inputType = InputType.TYPE_TEXT_VARIATION_PASSWORD
        transformationMethod = PasswordTransformationMethod()
    }

    /**
     * This method will return the entered password inside the [PasswordTextField].
     *
     * @throws PasswordNotLongEnoughException : If the length of the password is less than minimum expected length.
     * @throws PasswordDoesntMatchRegExException : If the password doesn't matches the provided [Regex] to [setPasswordValidationRegex].
     * */
    @Throws(PasswordNotLongEnoughException::class, PasswordDoesntMatchRegExException::class)
    fun enteredPassword(): String {
        if (text.toString().length < minEms) {
            throw PasswordNotLongEnoughException(context.getString(R.string.password_length_error, minEms))
        } else if (passwordRegex != null && !isRegexMatches(text.toString())) {
            throw PasswordDoesntMatchRegExException(context.getString(R.string.password_dont_match_error))
        } else {
            return text.toString()
        }
    }

    /**
     * This methods sets the minimum length for the password.
     * @param length : Sets the minimum length of password.
     * @throws IllegalArgumentException : If the length is less than zero it will throw [IllegalArgumentException].
     * */
    @Throws(IllegalArgumentException::class)
    fun setMinimumPasswordLength(length: Int) {
        if (length >= 0) {
            minEms = length
        } else {
            throw IllegalArgumentException("Password length cannot be negative")
        }
    }

    /**
     * This methods sets the regular expression that should be matched with the password.
     * @param regex : Sets the regular expression.
     * */
    fun setPasswordValidationRegex(regex: String?) {
        passwordRegex = regex
    }

    private fun isRegexMatches(password: String): Boolean {
        return if (!passwordRegex.isNullOrEmpty()) {
            Pattern.compile(passwordRegex!!).matcher(password).matches()
        } else {
            false
        }
    }

    companion object {
        private var passwordRegex: String? = null
    }
}
