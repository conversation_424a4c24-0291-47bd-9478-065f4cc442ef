package com.suryadigital.leo.libui.textdropdown

import android.widget.BaseAdapter
import android.widget.SpinnerAdapter

abstract class AbstractTextDropDownAdapter<T>(
    private val items: List<T>,
) :
    BaseAdapter(), SpinnerAdapter {

    override fun getCount(): Int {
        return items.size
    }

    override fun getItem(position: Int): Any? {
        return items[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }
}
