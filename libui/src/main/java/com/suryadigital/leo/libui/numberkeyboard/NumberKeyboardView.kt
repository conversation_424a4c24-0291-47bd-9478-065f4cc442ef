package com.suryadigital.leo.libui.numberkeyboard

import android.content.Context
import android.graphics.drawable.RippleDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import com.suryadigital.leo.libui.R

class NumberKeyboardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

    private lateinit var inputListener: InputListener
    private var input: String = ""
    private var pinLength: Int = 0
    private val keyboardView: View =
        LayoutInflater.from(context).inflate(R.layout.layout_number_keyboard, this, true)

    init {
        registerButtonListener(keyboardView)
    }

    /**
     * Register to this listener in order to get input from the keyboard.
     * */
    interface InputListener {
        fun onInputTap(num: String)
    }

    /**
     * This method setup the inputListener and allowed pin length. This method must be called
     * before using the click action on keyboard, else it will throw [UninitializedPropertyAccessException].
     *
     * @param inputListener : Listener which provides input callback to user.
     * @param pinLength: Sets the allowed PIN length, the allowed pin length is 4 to 10.
     * */
    @Throws(UninitializedPropertyAccessException::class, IllegalArgumentException::class)
    fun setup(inputListener: InputListener, pinLength: Int = 4) {
        this.inputListener = inputListener
        if (pinLength in 4 until 11) {
            this.pinLength = pinLength
        } else {
            throw IllegalArgumentException("PIN length can't be less than 4 or greater than 10")
        }
    }

    private fun View.setBackgroundRipple() {
        val rippleDrawable = ResourcesCompat.getDrawable(
            context.resources,
            R.drawable.button_ripple_background,
            context.theme,
        ) as RippleDrawable
        rippleDrawable.radius = 120
        background = rippleDrawable
    }

    private fun registerButtonListener(view: View) {
        view.apply {
            findViewById<TextView>(R.id.one).apply { init("1") }
            findViewById<TextView>(R.id.two).apply { init("2") }
            findViewById<TextView>(R.id.three).apply { init("3") }
            findViewById<TextView>(R.id.four).apply { init("4") }
            findViewById<TextView>(R.id.five).apply { init("5") }
            findViewById<TextView>(R.id.six).apply { init("6") }
            findViewById<TextView>(R.id.seven).apply { init("7") }
            findViewById<TextView>(R.id.eight).apply { init("8") }
            findViewById<TextView>(R.id.nine).apply { init("9") }
            findViewById<TextView>(R.id.zero).apply { init("0") }
            findViewById<ImageButton>(R.id.delete).apply {
                setOnClickListener { deleteDigit() }
                setBackgroundRipple()
            }
        }
    }

    private fun View.init(num: String) {
        setOnClickListener { writeNumber(num) }
        setBackgroundRipple()
    }

    private fun writeNumber(num: String) {
        if (input.length < pinLength) {
            inputListener.onInputTap(concatenateDigit(num))
        } else {
            // Not allowing any writing for more than 10^12 digits
        }
    }

    private fun concatenateDigit(digit: String): String {
        input += digit
        return input
    }

    private fun deleteDigit() {
        if (input.length > 1) {
            input = input.substring(0, input.length - 1)
        } else {
            input = ""
        }
        inputListener.onInputTap(input)
    }

    /**
     * This method clears the number memorized by [NumberKeyboardView] and return the
     * empty string to the caller.
     * */
    fun clear() {
        input = ""
        inputListener.onInputTap(input)
    }
}
