package com.suryadigital.leo.libui.datepicker

import android.app.DatePickerDialog
import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import androidx.annotation.StyleRes
import androidx.fragment.app.DialogFragment
import com.suryadigital.leo.libui.R
import java.time.LocalDate

/**
 * Implements the date picker dialog fragment that can be used as a dialog as well as an embedded date picker fragment.
 * */
class DatePicker :
    DialogFragment(),
    DatePickerDialog.OnDateSetListener,
    DatePickerDialogInterface {
    private var onDateChangeListener: OnDateChangeListener? = null
    private var selectedTheme: Int = R.style.DatePickerDialogTheme

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val selectedDate = LocalDate.now()
        return DatePickerDialog(
            requireContext(),
            selectedTheme,
            this,
            selectedDate.year,
            selectedDate.monthValue,
            selectedDate.dayOfMonth,
        )
    }

    /**
     * @param dateChangeListener : Sets the date change listener.
     * */
    override fun setDateChangeListener(dateChangeListener: OnDateChangeListener) {
        onDateChangeListener = dateChangeListener
    }

    /**
     * This method set the custom color theme to the date picker dialog.
     * @param theme : Resource id of custom theme.
     * */
    override fun setDatePickerTheme(
        @StyleRes theme: Int,
    ) {
        selectedTheme = theme
    }

    /**
     * This method will return the current/today's date.
     * */
    fun currentDate(): LocalDate = LocalDate.now()

    /**
     * An interface which provides the callback when the user taps OK/Cancel in the date picker dialog.
     * @return date : A selected local date object.
     * */
    interface OnDateChangeListener {
        fun onDateChanged(date: LocalDate)

        fun dialogCancelled()
    }

    override fun onDateSet(
        view: android.widget.DatePicker?,
        year: Int,
        month: Int,
        dayOfMonth: Int,
    ) {
        onDateChangeListener?.onDateChanged(LocalDate.of(year, month + 1, dayOfMonth))
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        onDateChangeListener?.dialogCancelled()
    }
}
