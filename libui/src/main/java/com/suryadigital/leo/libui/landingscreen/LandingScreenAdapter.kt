package com.suryadigital.leo.libui.landingscreen

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.suryadigital.leo.libui.R

class LandingScreenAdapter(
    private val landingScreenDataCount: Int,
    private val attributes: LandingScreenAttributes,
    private val landingScreenViewsConfigurer: LandingScreenViewsConfigurer,
) : RecyclerView.Adapter<LandingScreenAdapter.LandingScreenViewHolder>() {
    class LandingScreenViewHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView) {
        private val titleTV: TextView = itemView.findViewById(R.id.title)
        private val subtitleTV: TextView = itemView.findViewById(R.id.subtitle)
        private val clipArtIV: ImageView = itemView.findViewById(R.id.clip_art)

        fun bind(
            position: Int,
            attributes: LandingScreenAttributes,
            landingScreenViewsConfigurer: LandingScreenViewsConfigurer,
        ) {
            handleText(attributes)
            landingScreenViewsConfigurer.configureLandingScreenViews(position, clipArtIV, titleTV, subtitleTV)
        }

        private fun handleText(attributes: LandingScreenAttributes) {
            titleTV.textSize = attributes.titleTextSize
            titleTV.setTextColor(attributes.titleColor)
            subtitleTV.textSize = attributes.subTitleTextSize
            subtitleTV.setTextColor(attributes.subTitleColor)
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): LandingScreenViewHolder =
        LandingScreenViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.landing_screen,
                parent,
                false,
            ),
        )

    override fun onBindViewHolder(
        holder: LandingScreenViewHolder,
        position: Int,
    ) {
        holder.bind(position, attributes, landingScreenViewsConfigurer)
    }

    override fun getItemCount(): Int = landingScreenDataCount
}

interface LandingScreenViewsConfigurer {
    fun configureLandingScreenViews(
        position: Int,
        imageView: ImageView,
        titleTV: TextView,
        subtitleTV: TextView,
    )
}
