package com.suryadigital.leo.libui.viewpager

import android.view.View
import android.view.ViewGroup
import androidx.viewpager.widget.PagerAdapter

/**
 * Implements the [ViewPagerAdapter] for [ViewPager] which handles the list of custom views.
 *
 * @param views : list of custom views that need to be displayed.
 * */

internal class ViewPagerAdapter(
    private val views: List<View>,
) : PagerAdapter() {
    override fun isViewFromObject(view: View, obj: Any): Boolean {
        return view == obj
    }

    override fun instantiateItem(collection: ViewGroup, position: Int): Any {
        collection.addView(views[position])
        return views[position]
    }

    override fun getCount(): Int {
        return views.size
    }

    override fun destroyItem(collection: ViewGroup, position: Int, view: Any) {
        collection.removeView(view as View?)
    }
}
