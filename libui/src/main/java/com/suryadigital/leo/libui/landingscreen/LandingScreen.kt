package com.suryadigital.leo.libui.landingscreen

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.core.graphics.drawable.DrawableCompat
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.suryadigital.leo.libui.R
import kotlin.properties.Delegates

/**
 * Implements a custom landing screen component with a skip button, a next button, and a page indicator
 * by using [ViewPager2] and [TabLayout].
 *
 * @attribute landingScreenBackgroundColor : Sets the color to the background of the landing screen
 * @attribute titleTextSize : Sets the text size of the title
 * @attribute subTitleTextSize : Sets the text size of the subtitle
 * @attribute finishButtonText : Sets the text of the button in the last screen
 * @attribute selectedTabColor : Sets the color to the tab of the current page in the landing screen
 * @attribute skipButtonColor : Sets the text color of skip button
 * @attribute nextButtonColor : Sets the text color of next button
 * @attribute finishButtonTextColor : Sets the color of finish button
 * @attribute titleColor : Sets the text color of title
 * @attribute subTitleColor : Sets the text color of sub title
 *
 * @property attributes: Holds the attributes associating with the LandingScreen
 * */

class LandingScreen : FrameLayout {
    private lateinit var viewPager: ViewPager2
    private lateinit var attributes: LandingScreenAttributes
    private lateinit var nextButton: Button
    private lateinit var skipButton: Button
    private lateinit var finishButton: Button
    private lateinit var tabLayout: TabLayout
    private var numberOfScreens by Delegates.notNull<Int>()
    private lateinit var landingScreenLayout: LinearLayout

    constructor(context: Context) : super(context) {
        initView(context, null)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        initView(context, attrs)
    }

    constructor(
        context: Context,
        attrs: AttributeSet,
        defStyleAttr: Int,
    ) : super(context, attrs, defStyleAttr, 0) {
        initView(context, attrs)
    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int,
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        initView(context, attrs)
    }

    private fun initView(
        context: Context,
        attrs: AttributeSet?,
    ) {
        val view = LayoutInflater.from(context).inflate(R.layout.landing_screen_layout, this, true)
        viewPager = view.findViewById(R.id.landing_screen_view_pager)
        attributes = LandingScreenAttributes(context, attrs)
        nextButton = view.findViewById(R.id.next)
        skipButton = view.findViewById(R.id.skip)
        finishButton = view.findViewById(R.id.finish)
        tabLayout = view.findViewById(R.id.tab_layout)
        landingScreenLayout = view.findViewById(R.id.landing_screen_layout)
        landingScreenLayout.backgroundTintList =
            ColorStateList.valueOf(attributes.landingScreenBackgroundColor)
    }

    private fun handleButtons(
        position: Int,
        listener: FinishButtonListener,
    ) {
        if (!attributes.shouldShowOnlyFinishButton) {
            if (position == numberOfScreens - 1) {
                nextButton.visibility = View.INVISIBLE
                skipButton.visibility = View.INVISIBLE
                finishButton.visibility = View.VISIBLE
                finishButton.setOnClickListener {
                    listener.onFinishButtonClicked()
                }
            } else {
                finishButton.visibility = View.GONE
                nextButton.apply {
                    visibility = View.VISIBLE
                    setOnClickListener {
                        viewPager.currentItem = position + 1
                    }
                }
                skipButton.apply {
                    visibility = View.VISIBLE
                    setOnClickListener {
                        viewPager.currentItem = numberOfScreens - 1
                    }
                }
            }
        }
        setButtonColor()
    }

    private fun setButtonColor() {
        skipButton.setTextColor(attributes.skipButtonColor)
        nextButton.setTextColor(attributes.nextButtonColor)
        finishButton.setTextColor(attributes.finishButtonTextColor)
        finishButton.text = attributes.finishButtonText
        finishButton.background = attributes.finishButtonBackground

        val backgroundDrawable: Drawable = finishButton.background
        DrawableCompat.setTint(backgroundDrawable, attributes.nextButtonColor)
    }

    /**
     * This method helps in launching the landing screen
     * @param landingScreenDataCount : Number of screens in the landing screen
     * */
    fun launch(
        landingScreenDataCount: Int,
        finishButtonListener: FinishButtonListener,
        landingScreenViewsConfigurer: LandingScreenViewsConfigurer,
    ) {
        numberOfScreens = landingScreenDataCount
        val viewPagerAdapter =
            LandingScreenAdapter(landingScreenDataCount, attributes, landingScreenViewsConfigurer)
        handleViewPager(viewPagerAdapter, finishButtonListener)
    }

    private fun handleViewPager(
        viewPagerAdapter: LandingScreenAdapter,
        finishButtonListener: FinishButtonListener,
    ) {
        viewPager.adapter = viewPagerAdapter
        if (attributes.shouldShowOnlyFinishButton) {
            nextButton.visibility = View.INVISIBLE
            skipButton.visibility = View.INVISIBLE
            finishButton.visibility = View.VISIBLE
            finishButton.setOnClickListener {
                finishButtonListener.onFinishButtonClicked()
            }
        }
        viewPager.registerOnPageChangeCallback(
            object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    handleButtons(position, finishButtonListener)
                }
            },
        )

        TabLayoutMediator(tabLayout, viewPager) { _, _ ->
            tabLayout.setSelectedTabIndicatorColor(attributes.selectedPageColor)
        }.attach()
    }

    interface FinishButtonListener {
        fun onFinishButtonClicked()
    }
}
