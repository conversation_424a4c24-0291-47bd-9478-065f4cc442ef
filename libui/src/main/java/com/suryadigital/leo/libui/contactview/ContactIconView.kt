package com.suryadigital.leo.libui.contactview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import com.suryadigital.leo.libui.R
import java.util.Locale

/**
 * Implementation of [ContactIconView] is done using the ImageView and TextView, it can display the initials for a contact or set an image for the contact.
 * you can call [setContactInitials] method with contact name as input and it will generate the initials from the name and
 * set it in place of contact image.
 *
 * @property imageView : ImageView where contact image will be set is exposed so that user can handle all possible cases of design and
 * loading it with different possible image types.
 * @property initialsTextView: Initials text view displays the initials in center of contact image view. It can be used to customize the text property.
 * */

class ContactIconView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : CardView(context, attrs, defStyleAttr) {

    val imageView: ImageView
    val initialsTextView: TextView

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.contact_view, this, true)
        imageView = view.findViewById(R.id.profile_image_view)
        initialsTextView = view.findViewById(R.id.initials_view)
    }

    /**
     * This method extracts the initials from the given name of specified [initialsMaxLength] and set to the image view.
     * The name can be of any number of words separated by ' '. The initials generated will be of length less than or equal to specified [initialsMaxLength].
     * If the name is blank then it will show blank contact image.
     *
     * @param name: The name of contact person.
     * @param initialsMaxLength: The length of initials to be displayed.
     * */
    fun setContactInitials(name: String, initialsMaxLength: Int = 2) {
        if (name.isNotBlank()) {
            initialsTextView.visibility = View.VISIBLE
            imageView.visibility = View.GONE
            name.split(' ')
                .mapNotNull { it.firstOrNull()?.toString() }
                .reduce { acc, s -> acc + s }
                .apply {
                    if (length > initialsMaxLength) {
                        val initials = substring(0, initialsMaxLength)
                        initialsTextView.text = initials.uppercase(Locale.getDefault())
                    } else {
                        initialsTextView.text = this.uppercase(Locale.getDefault())
                    }
                }
        }
    }
}
