package com.suryadigital.leo.libui.listview

import androidx.recyclerview.widget.RecyclerView

/**
 * An abstract list adapter that can be extended to create custom recycler view adapters.
 */
abstract class ListAdapter<T, VH : RecyclerView.ViewHolder>(items: List<T>) :
    RecyclerView.Adapter<VH>() {

    val filterItemList: ArrayList<T> = arrayListOf()
    private var onItemClickListener: OnItemClickListener? = null

    init {
        filterItemList.addAll(items)
    }

    /**
     * Override this method in order to bind the data to the view inflated using [onCreateViewHolder]
     * in adapter.
     * */
    abstract fun onBindView(holder: VH, position: Int)

    /**
     * Filters data list based on [query].
     */
    abstract fun filter(query: String)

    override fun getItemCount(): Int = filterItemList.size

    /**
     * Override the [onBindViewHolder] from [RecyclerView.Adapter] in order to provide the click
     * event and position of views in recycler view to you.
     * */
    final override fun onBindViewHolder(holder: VH, position: Int) {
        holder.apply {
            itemView.setOnClickListener {
                onItemClickListener?.onItemClicked(position)
            }
            onBindView(this, position)
        }
    }

    /**
     * Registers [OnItemClickListener]
     */
    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    /**
     * To receive click events on list item, you must register to [OnItemClickListener].
     */
    interface OnItemClickListener {
        /**
         * Called when user clicks on list item.
         */
        fun onItemClicked(pos: Int)
    }
}
