package com.suryadigital.leo.libui.contactpicker

import android.content.Context
import android.widget.BaseAdapter
import android.widget.ImageView
import androidx.core.net.toUri
import androidx.lifecycle.ViewModelProvider
import com.suryadigital.leo.libui.utils.launchOnMainDispatcher
import java.util.Locale

/**
 * To provide the custom view implementation extend the [ContactListAdapter] and override [getView] method.
 * Use [filterContactList] as list of contact in the [getView] implementation. Also, Add your own filter
 * function by override the [filter] method provided by [Filterable].
 *
 * @param context : Sets the context.
 * @param initialFilter : The initial filter function according to which list will be filtered
 * and displayed initially.
 * */
abstract class ContactListAdapter(
    private val context: Context,
    private val initialFilter: (Contact) -> Boolean = { true },
) : BaseAdapter(), Filterable {

    val filterContactList: ArrayList<Contact> = arrayListOf()
    private val contactPickerViewModel =
        ViewModelProvider.NewInstanceFactory().create(ContactPickerViewModel::class.java)
    private var contacts = arrayListOf<Contact>()

    /**
     * Gets resource id of selected contact item icon.
     * */
    abstract fun getSelectedContactIcon(): Int

    /**
     * Gets the resource id of default contact icon.
     * */
    abstract fun getDefaultContactIcon(): Int

    /**
     * Initializes the list of contacts and notifies the adapter on the Main Dispatcher.
     * Clearing the contact list before adding contacts to avoid showing duplicate contact items in the list.
     */
    suspend fun initContactsList() {
        if (checkContactPermission(context)) {
            contacts = contactPickerViewModel.fetchContacts(context = context)
            filterContactList.clear()
            filterContactList.addAll(contacts.filter(initialFilter))
            launchOnMainDispatcher(::notifyDataSetChanged)
        }
    }

    /**
     * During the creation of custom adapter call this method in order to set the item selection feature.
     *
     * @param imageView : ImageView on which selected icon will be displayed.
     * @param position : Position of list item.
     * */
    fun setSelectionIconIfItemIsSelected(imageView: ImageView, position: Int) {
        val imageUri = filterContactList[position].imageUri
        if (filterContactList[position].isSelected) {
            imageView.setImageResource(getSelectedContactIcon())
        } else {
            if (imageUri != null) {
                imageView.setImageURI(imageUri.toUri())
            } else {
                imageView.setImageResource(getDefaultContactIcon())
            }
        }
    }

    final override fun getItem(position: Int): Contact {
        return filterContactList[position]
    }

    final override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    final override fun getCount(): Int {
        return filterContactList.size
    }

    /**
     * A filter function which filters the contacts on the basis of their name and contact number matches.
     * */
    override fun filter(queryString: String) {
        filterContactList.clear()
        if (queryString.isEmpty()) {
            filterContactList.addAll(contacts)
        } else {
            contacts.filterTo(filterContactList) {
                it.name.lowercase(Locale.getDefault()).contains(
                    queryString.lowercase(
                        Locale.getDefault(),
                    ),
                ) || it.contactNumber.contains(
                    queryString.lowercase(
                        Locale.getDefault(),
                    ),
                )
            }
        }
        if (filterContactList.isEmpty() && isNumber(queryString)) {
            filterContactList.add(
                Contact(
                    name = "New Number",
                    contactNumber = queryString,
                ),
            )
        }
        notifyDataSetChanged()
    }

    private fun isNumber(queryString: String): Boolean {
        return try {
            queryString.toLong()
            true
        } catch (e: Exception) {
            false
        }
    }
}
