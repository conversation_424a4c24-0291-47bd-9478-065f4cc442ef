package com.suryadigital.leo.libui.viewpager

import android.graphics.Rect
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import kotlin.math.abs

/**
 * Implements the touch and hold feature, It also perfroms checks for [touchStayedWithinViewBounds]
 * which tell's if the touch is still inside view area, it will fire up cancel event when users moves the touch outside.
 * */
internal abstract class ViewPagerTouchListener : OnTouchListener {
    private var touchStayedWithinViewBounds = false

    override fun onTouch(
        view: View,
        event: MotionEvent,
    ): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                x1 = event.x
                y1 = event.y
                touchStayedWithinViewBounds = true
                onDownTouchAction()
            }
            MotionEvent.ACTION_UP ->
                if (touchStayedWithinViewBounds) {
                    if (abs(x1 - event.x) < THRESHOLD_VALUE &&
                        abs(
                            y1 - event.y,
                        ) < THRESHOLD_VALUE
                    ) {
                        onUpTouchAction()
                    }
                }
            MotionEvent.ACTION_MOVE ->
                if (touchStayedWithinViewBounds &&
                    isMotionEventInsideView(view, event)
                ) {
                    onCancelTouchAction()
                    touchStayedWithinViewBounds = false
                }
            MotionEvent.ACTION_CANCEL -> {
                onCancelTouchAction()
            }
            else -> {
                onCancelTouchAction()
            }
        }
        return false // Not consuming any event.
    }

    abstract fun onDownTouchAction()

    abstract fun onUpTouchAction()

    abstract fun onCancelTouchAction()

    private fun isMotionEventInsideView(
        view: View,
        event: MotionEvent,
    ): Boolean {
        val viewRect =
            Rect(
                view.left,
                view.top,
                view.right,
                view.bottom,
            )
        return viewRect.contains(
            view.left + event.x.toInt(),
            view.top + event.y.toInt(),
        )
    }

    companion object {
        private const val THRESHOLD_VALUE = 10f
        private var x1 = 0f
        private var y1 = 0f
    }
}
