package com.suryadigital.leo.libui.textdropdown

import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.TypedValue
import androidx.appcompat.widget.AppCompatSpinner
import androidx.core.content.ContextCompat

class TextDropdown(context: Context, attrs: AttributeSet? = null) :
    AppCompatSpinner(context, attrs) {

    private var itemSelectedListener: OnItemSelectedListener? = null

    init {
        dropDownVerticalOffset = convertDpToPixel(verticalOffset)
        TypedValue().apply {
            getContext().theme
                .resolveAttribute(android.R.attr.selectableItemBackground, this, true)
            foreground = ContextCompat.getDrawable(context, resourceId)
        }
    }

    /**
     * This method will set the custom adapter provided to the dropdown.
     * @param adapter : Adapter needed to be set in the dropdown.
     * */
    fun <T> setAdapter(adapter: AbstractTextDropDownAdapter<T>) {
        super.setAdapter(adapter)
    }

    override fun setSelection(position: Int) {
        super.setSelection(position)
        if (position == selectedItemPosition) {
            itemSelectedListener?.onItemSelected(
                this,
                selectedView,
                position,
                selectedItemId,
            )
        }
    }

    /**
     * This method will dismiss the popup generated by `AppCompatSpinner` when `onDetachedFromWindow`
     * is called.
     * Thanks: https://stackoverflow.com/a/18076575
     */
    fun dismiss() {
        super.onDetachedFromWindow()
    }

    /**
     * It provides the callback for selected item even if it is unchanged for ex. if you have selected the third item
     * and again you are selecting the same you will get callback.
     * */
    fun setOnItemSelectedEvenIfUnchangedListener(
        listener: OnItemSelectedListener,
    ) {
        itemSelectedListener = listener
    }

    companion object {
        private const val verticalOffset: Float = 30f
    }

    /**
     * A helper method to convert dp tp pixel.
     * */
    private fun convertDpToPixel(dp: Float): Int {
        val displayFactor = context.resources
            .displayMetrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT
        return (dp * displayFactor).toInt()
    }
}
