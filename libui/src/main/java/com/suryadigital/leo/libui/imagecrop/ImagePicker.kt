package com.suryadigital.leo.libui.imagecrop

import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.provider.MediaStore
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.R
import com.yalantis.ucrop.UCrop
import java.io.File
import java.util.UUID

object ImagePicker {

    /**
     * `imagePickerFailure` is called whenever an error occurs while picking up the image. User of
     * this class must implement `ImagePickerFailureListener` to receive failure events.
     */
    interface ImagePickerFailureListener {
        fun imagePickerFailure(error: Exception)
    }

    private lateinit var imagePickerFailureListener: ImagePickerFailureListener
    private var capturedImageUri: Uri? = null

    fun setImagePickerFailureListener(imagePickerFailureListener: ImagePickerFailureListener) {
        this.imagePickerFailureListener = imagePickerFailureListener
    }

    /**
     * Opens the image gallery to pick an image.
     */
    fun openImageGallery(fragment: Fragment) {
        val intent = Intent(Intent.ACTION_GET_CONTENT)
        intent.type = INTENT_TYPE
        fragment.startActivityForResult(intent, IMAGE_PICKER_GALLERY_REQUEST_CODE)
    }

    /**
     * Opens the camera app to capture an image.
     */
    fun openCamera(fragment: Fragment) {
        val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        capturedImageUri = getImageUri(fragment)
        takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, capturedImageUri)
        fragment.startActivityForResult(takePictureIntent, IMAGE_PICKER_CAMERA_REQUEST_CODE)
    }

    /**
     * @param data contains the path of the selected image from gallery
     * @param fragment to prove context and launch activity to crop the image
     * @param width max output width
     * @param height max output height
     * @param outputImageType Type of the out image. As of now we are supporting only JPEG
     */
    fun cropImage(
        data: Intent?,
        fragment: Fragment,
        width: Int = 400,
        height: Int = 400,
        compressionQuality: Int = 60,
        outputImageType: OutputImageType = OutputImageType.JPEG,
        freeStyleCropEnabled: Boolean = false,
    ) {
        val uri = data?.data

        val imageType = when (outputImageType) {
            OutputImageType.JPEG -> Bitmap.CompressFormat.JPEG
            OutputImageType.PNG -> Bitmap.CompressFormat.PNG
        }

        // If Uri for image selected from gallery is null then we check for the Uri of image captured
        // by camera.
        when {
            uri != null -> {
                cropImage(
                    uri,
                    fragment,
                    width,
                    height,
                    compressionQuality,
                    imageType,
                    freeStyleCropEnabled,
                )
            }
            capturedImageUri != null -> {
                cropImage(
                    capturedImageUri!!,
                    fragment,
                    width,
                    height,
                    compressionQuality,
                    imageType,
                    freeStyleCropEnabled,
                )
            }
            else -> {
                imagePickerFailureListener.imagePickerFailure(IllegalArgumentException("Invalid Uri for selected image"))
            }
        }
    }

    private fun cropImage(
        sourceImage: Uri,
        fragment: Fragment,
        width: Int,
        height: Int,
        compressionQuality: Int,
        imageType: Bitmap.CompressFormat,
        freeStyleCropEnabled: Boolean,
    ) {
        val context = fragment.requireContext()
        val options = UCrop.Options()

        if (!freeStyleCropEnabled) {
            options.withAspectRatio(1F, 1F)
        }
        options.withMaxResultSize(width, height)
        options.setHideBottomControls(true)
        options.setCompressionQuality(compressionQuality)
        options.setFreeStyleCropEnabled(freeStyleCropEnabled)
        options.setCropGridStrokeWidth(1)
        options.setCropGridColumnCount(2)
        options.setCropGridRowCount(2)
        options.setCompressionFormat(imageType)
        options.setToolbarColor(ContextCompat.getColor(context, R.color.colorPrimary))
        options.setStatusBarColor(ContextCompat.getColor(context, R.color.colorPrimary))
        options.setToolbarWidgetColor(ContextCompat.getColor(context, android.R.color.white))

        // UUID is being used to avoid a condition where the app is uploading the cropped image &
        // user selects the same image and crops it again in which case the previous image will be
        // overwritten while being uploaded.
        UCrop.of(
            sourceImage,
            Uri.fromFile(File(fragment.requireActivity().cacheDir, "cropped_${UUID.randomUUID()}")),
        ).withOptions(options).start(context, fragment)
    }

    private fun getImageUri(fragment: Fragment): Uri {
        val tempFile = File(fragment.requireActivity().cacheDir, "captured_${UUID.randomUUID()}")
        return FileProvider.getUriForFile(
            fragment.requireContext(),
            getFileProviderAuthority(fragment),
            tempFile,
        )
    }

    /**
     * File provider authority requires to get FileProvider for the app which will give a secure
     * `content://` Uri instead of `file://` Uri.
     * See more: https://developer.android.com/reference/androidx/core/content/FileProvider
     *
     * This authority should be same in the manifest provider.
     * The app should paste this code as is.
     *
     * ```
     *   <provider
     *      android:name="androidx.core.content.FileProvider"
     *      android:authorities="${applicationId}.provider" *This should be same*
     *      android:exported="false"
     *      android:grantUriPermissions="true">
     *      <meta-data
     *          android:name="android.support.FILE_PROVIDER_PATHS"
     *          android:resource="@xml/file_provider_path"/>
     *   </provider>
     * ```
     */
    private fun getFileProviderAuthority(fragment: Fragment): String {
        return "${fragment.requireContext().applicationContext.packageName}.provider"
    }

    /**
     * Provides Uri for cropped image
     */
    fun getOutput(data: Intent): Uri? {
        capturedImageUri = null
        return UCrop.getOutput(data)
    }

    /**
     * Gives error occurred while cropping the image
     */
    fun getError(data: Intent): Throwable? = UCrop.getError(data)

    const val IMAGE_PICKER_GALLERY_REQUEST_CODE: Int = 200
    const val IMAGE_PICKER_CAMERA_REQUEST_CODE: Int = 300
    const val IMAGE_CROP_REQUEST_CODE: Int = UCrop.REQUEST_CROP
    const val IMAGE_CROP_RESULT_ERROR_CODE: Int = UCrop.RESULT_ERROR

    private const val INTENT_TYPE: String = "image/*"

    enum class OutputImageType {
        JPEG, PNG
    }
}
