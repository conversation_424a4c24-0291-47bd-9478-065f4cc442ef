package com.suryadigital.leo.libui.landingscreen

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.suryadigital.leo.libui.R

/**
 * This class handle all the attributes provided through XML layout, it performs initialization of the attributes
 * */
class LandingScreenAttributes(
    context: Context,
    attrs: AttributeSet?,
) {
    var selectedPageColor: Int = context.getColor(R.color.name_label_color)
    var skipButtonColor: Int = context.getColor(R.color.name_label_color)
    var nextButtonColor: Int = context.getColor(R.color.name_label_color)
    var finishButtonTextColor: Int = Color.WHITE
    var landingScreenBackgroundColor: Int = context.getColor(R.color.landingScreenBgColor)
    var titleColor: Int = context.getColor(R.color.name_label_color)
    var subTitleColor: Int = context.getColor(R.color.name_label_color)
    var finishButtonText: String = context.getString(R.string.finish)
    var finishButtonBackground: Drawable? =
        ContextCompat.getDrawable(context, R.drawable.bg_button_4dp_primary)
    var titleTextSize: Float = 18f
    var subTitleTextSize: Float = 14f
    var shouldShowOnlyFinishButton: Boolean = false

    init {
        val attributes = context.obtainStyledAttributes(attrs, R.styleable.LandingScreen, 0, 0)
        try {
            selectedPageColor =
                attributes.getColor(
                    R.styleable.LandingScreen_selectedPageColor,
                    selectedPageColor,
                )
            skipButtonColor =
                attributes.getColor(
                    R.styleable.LandingScreen_skipButtonColor,
                    skipButtonColor,
                )
            nextButtonColor =
                attributes.getColor(
                    R.styleable.LandingScreen_nextButtonColor,
                    nextButtonColor,
                )
            finishButtonTextColor =
                attributes.getColor(
                    R.styleable.LandingScreen_finishButtonTextColor,
                    finishButtonTextColor,
                )
            landingScreenBackgroundColor =
                attributes.getColor(
                    R.styleable.LandingScreen_landingScreenBackgroundColor,
                    landingScreenBackgroundColor,
                )
            titleColor =
                attributes.getColor(
                    R.styleable.LandingScreen_titleColor,
                    titleColor,
                )
            subTitleColor =
                attributes.getColor(
                    R.styleable.LandingScreen_subTitleColor,
                    subTitleColor,
                )
            finishButtonText = attributes.getString(
                R.styleable.LandingScreen_finishButtonText,
            ) ?: finishButtonText
            finishButtonBackground = attributes.getDrawable(
                R.styleable.LandingScreen_finishButtonBackground,
            ) ?: finishButtonBackground
            /*
                Dividing text dimensions with scaled density because the dimension values we are
                getting from attributed is the scaled up values because of which the values we are
                mentioning in attributes are not correct.
                For example: On Samsung S8 scaledDensity = 3
                so if we mention app:titleTextSize="18sp"
                here we are getting value as 18*3=54 and then we are assigning 54 as title text size
                which is way bigger than text size 18.
             */
            val scaledDensity = context.resources.displayMetrics.scaledDensity
            titleTextSize = attributes.getDimension(
                R.styleable.LandingScreen_titleTextSize,
                titleTextSize,
            ) / scaledDensity
            subTitleTextSize = attributes.getDimension(
                R.styleable.LandingScreen_subTitleTextSize,
                subTitleTextSize,
            ) / scaledDensity
            shouldShowOnlyFinishButton =
                attributes.getBoolean(
                    R.styleable.LandingScreen_shouldShowOnlyFinishButton,
                    shouldShowOnlyFinishButton,
                )
        } finally {
            attributes.recycle()
        }
    }
}
