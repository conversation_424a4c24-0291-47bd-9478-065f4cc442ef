package com.suryadigital.leo.libui.progressbutton

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.util.AttributeSet
import com.suryadigital.leo.libui.R

internal class ProgressButtonAttributes(
    context: Context,
    attributeSet: AttributeSet?,
) {
    val progressButtonText: String
    val progressButtonTextColor: Int
    val progressButtonRadius: Float
    val progressButtonBackgroundColor: Int
    val progressButtonElevation: Float
    val buttonTextFontStyle: Int

    init {
        context.obtainStyledAttributes(attributeSet, R.styleable.ProgressButton).run {
            progressButtonText =
                getString(R.styleable.ProgressButton_buttonText) ?: ""
            progressButtonTextColor =
                getColor(R.styleable.ProgressButton_buttonTextColor, Color.BLACK)
            progressButtonRadius =
                getDimension(R.styleable.ProgressButton_buttonRadius, 2f)
            progressButtonBackgroundColor =
                getColor(R.styleable.ProgressButton_buttonBackgroundColor, Color.LTGRAY)
            progressButtonElevation =
                getDimension(R.styleable.ProgressButton_buttonElevation, 4f)
            buttonTextFontStyle =
                getInt(R.styleable.ProgressButton_buttonTextFontStyle, Typeface.NORMAL)
            recycle()
        }
    }
}
