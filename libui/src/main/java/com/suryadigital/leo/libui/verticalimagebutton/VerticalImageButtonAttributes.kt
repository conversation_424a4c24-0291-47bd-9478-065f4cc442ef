package com.suryadigital.leo.libui.verticalimagebutton

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Log
import androidx.core.content.res.ResourcesCompat
import com.suryadigital.leo.libui.R

class VerticalImageButtonAttributes(
    context: Context,
    attributeSet: AttributeSet?,
) {
    companion object {
        @JvmStatic
        internal val DEFAULT_LAYOUT_WEIGHT = 5f

        @JvmStatic
        internal val DEFAULT_LABEL_TEXT = ""

        @JvmStatic
        private val DEFAULT_ICON_WIDTH = -1f

        @JvmStatic
        private val DEFAULT_ICON_PADDING = 0f

        @JvmStatic
        private val DEFAULT_ICON_LABEL_SEPARATION = 0f

        @JvmStatic
        private val DEFAULT_ICON_BACKGROUND_CORNER_RADIUS = 0f

        @JvmStatic
        private val DEFAULT_ICON_BACKGROUND_COLOR = Color.TRANSPARENT

        @JvmStatic
        private val DEFAULT_LABEL_TEXT_SIZE = 14

        @JvmStatic
        private val DEFAULT_LABEL_TEXT_COLOR = Color.BLACK

        @JvmStatic
        private val DEFAULT_RIPPLE_RADIUS = 100
    }

    var iconSource: Drawable? internal set
    var labelText: String internal set
    var iconHeightPercent: Float internal set
    var iconLabelSeparation: Float internal set
    var iconBackgroundCornerRadius: Float internal set
    var labelTextSize: Int internal set
    var iconWidth: Float internal set
    var iconPadding: Float internal set
    var iconBackgroundColor: Int internal set
    var labelTextColor: Int internal set
    var labelTextFontFamily: Typeface? internal set
    var labelTextFontStyle: Int internal set
    var rippleRadius: Int internal set

    init {
        context.obtainStyledAttributes(attributeSet, R.styleable.VerticalImageButton).run {

            iconSource = getDrawable(R.styleable.VerticalImageButton_iconSource)

            labelText = getText(R.styleable.VerticalImageButton_labelText)?.toString()
                ?: run { DEFAULT_LABEL_TEXT }

            iconWidth =
                getDimension(
                    R.styleable.VerticalImageButton_iconWidth,
                    DEFAULT_ICON_WIDTH,
                )

            iconHeightPercent =
                getFloat(
                    R.styleable.VerticalImageButton_iconHeightPercent,
                    DEFAULT_LAYOUT_WEIGHT,
                )

            iconLabelSeparation =
                getDimension(
                    R.styleable.VerticalImageButton_iconLabelSeparation,
                    DEFAULT_ICON_LABEL_SEPARATION,
                )

            iconBackgroundCornerRadius =
                getDimension(
                    R.styleable.VerticalImageButton_iconBackgroundCornerRadius,
                    DEFAULT_ICON_BACKGROUND_CORNER_RADIUS,
                )

            labelTextSize =
                getDimensionPixelSize(
                    R.styleable.VerticalImageButton_labelTextSize,
                    DEFAULT_LABEL_TEXT_SIZE,
                )

            iconPadding =
                getDimension(
                    R.styleable.VerticalImageButton_iconPadding,
                    DEFAULT_ICON_PADDING,
                )

            iconBackgroundColor =
                getColor(
                    R.styleable.VerticalImageButton_iconBackgroundColor,
                    DEFAULT_ICON_BACKGROUND_COLOR,
                )

            labelTextColor =
                getColor(
                    R.styleable.VerticalImageButton_labelTextColor,
                    DEFAULT_LABEL_TEXT_COLOR,
                )

            // Font family can be directly obtained from using `getFont`, but it requires min SDK of 26.
            val fontRecourseID =
                getResourceId(
                    R.styleable.VerticalImageButton_labelTextFontFamily,
                    0,
                )

            labelTextFontFamily =
                if (fontRecourseID != 0) {
                    try {
                        ResourcesCompat.getFont(context, fontRecourseID)
                    } catch (notFoundException: Resources.NotFoundException) {
                        Log.w(
                            "VerticalImageButton:",
                            "Unable to find the specified font family, reverting to the default.",
                        )
                        null
                    }
                } else {
                    null
                }

            labelTextFontStyle =
                getInt(R.styleable.VerticalImageButton_labelTextFontStyle, Typeface.NORMAL)

            rippleRadius =
                getInt(
                    R.styleable.VerticalImageButton_rippleRadius,
                    DEFAULT_RIPPLE_RADIUS,
                )
            recycle()
        }
    }
}
