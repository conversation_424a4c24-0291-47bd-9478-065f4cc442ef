package com.suryadigital.leo.libui.location.model

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import androidx.annotation.DrawableRes
import okhttp3.HttpUrl
import java.io.File

/**
 * This class holds map marker image source.
 *
 * User must use these sources for passing marker image.
 *
 * The default supported data types for thumbnail are:
 * - [StringSource] (mapped to a [UriSource])
 * - [HttpUrlSource]
 * - [UriSource] ("android.resource", "content", "file", "http", and "https" schemes only)
 * - [FileSource]
 * - @DrawableRes [IntSource]
 * - [DrawableSource]
 * - [BitmapSource]
 */
sealed class LocationMarkerSource
data class StringSource(val source: String) : LocationMarkerSource()
data class HttpUrlSource(val source: HttpUrl) : LocationMarkerSource()
data class UriSource(val source: Uri) : LocationMarkerSource()
data class FileSource(val source: File) : LocationMarkerSource()
data class IntSource(@DrawableRes val source: Int) : LocationMarkerSource()
data class DrawableSource(val source: Drawable) : LocationMarkerSource()
data class BitmapSource(val source: Bitmap) : LocationMarkerSource()
