package com.suryadigital.leo.libui.otptextfield

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status

internal class SmsBroadcastReceiver(
    private val userConsentRequired: <PERSON>olean,
) : BroadcastReceiver() {
    private var otpListener: ReceiverOTPListener? = null

    override fun onReceive(
        context: Context,
        intent: Intent,
    ) {
        if (SmsRetriever.SMS_RETRIEVED_ACTION == intent.action) {
            val extras = intent.extras
            val status: Status = extras!![SmsRetriever.EXTRA_STATUS] as Status
            when (status.statusCode) {
                CommonStatusCodes.SUCCESS -> {
                    val message: String? = extras[SmsRetriever.EXTRA_SMS_MESSAGE] as String?
                    if (!userConsentRequired && message != null) {
                        otpListener?.onSMSReceived(message)
                    } else {
                        extras.getParcelable<Intent>(SmsRetriever.EXTRA_CONSENT_INTENT)?.apply {
                            otpListener?.onConsentActivityStart(this, SMS_CONSENT_REQUEST)
                        }
                    }
                }
                CommonStatusCodes.TIMEOUT -> {
                    otpListener?.onTimeOut()
                }
            }
        }
    }

    fun setOtpListener(otpListener: ReceiverOTPListener) {
        this.otpListener = otpListener
    }

    interface ReceiverOTPListener {
        fun onSMSReceived(message: String)

        fun onTimeOut()

        fun onConsentActivityStart(
            intent: Intent,
            requestCode: Int,
        )
    }

    companion object {
        private const val SMS_CONSENT_REQUEST: Int = 2001
    }
}
