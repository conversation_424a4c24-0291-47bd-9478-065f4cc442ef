package com.suryadigital.leo.libui.contactpicker

import android.text.Editable
import android.text.TextWatcher

internal abstract class EditTextWatcher : TextWatcher {

    abstract fun onTextChanged(s: String)

    override fun afterTextChanged(s: Editable?) {
        onTextChanged(s.toString())
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
    }
}
