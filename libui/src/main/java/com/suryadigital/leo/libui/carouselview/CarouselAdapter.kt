package com.suryadigital.leo.libui.carouselview

import androidx.recyclerview.widget.RecyclerView
import kotlin.math.pow

/**
 * Implements the abstract carousel adapter that can be extended to create carousel view.
 * */
abstract class CarouselAdapter<T, VH : RecyclerView.ViewHolder>(private val items: List<T>) :
    RecyclerView.Adapter<VH>() {

    internal val count = items.size
    private var maxValue: Int = 0
    private var onCarouselClickListener: CarouselRecyclerView.OnCarouselClickListener? = null

    /**
     * Override this method in order to bind the data to the view inflated using [onCreateViewHolder] in adapter.
     * */
    abstract fun onBindView(holder: VH, position: Int)

    /**
     * This returns the recycler view item count.
     * */
    final override fun getItemCount(): Int {
        return if (items.isEmpty()) {
            0
        } else if (items.size == 1) {
            1
        } else {
            if (maxValue == 0) {
                kDigitNumberDivisibleBySize(items.size, 9).toInt()
            } else {
                maxValue
            }
        }
    }

    private fun getActualPosition(pos: Int): Int = pos % count

    /**
     * Implements the [onCarouselClickListener] listener, returns the position of item in carousel.
     * */
    fun setOnItemClickListener(onCarouselClickListener: CarouselRecyclerView.OnCarouselClickListener) {
        this.onCarouselClickListener = onCarouselClickListener
    }

    /**
     * @param count : scroll space count
     * Sets the scroll space as multiple of [items] size to count
     * The number specified should be in the integer range 0..Integer.MAX_VALUE
     *
     * @suppress('NotifyDataSetChanged') is here because we are updating the complete list and there
     * is no other method which can be used here.
     * */
    @SuppressWarnings("NotifyDataSetChanged")
    fun setScrollSpace(count: Int) {
        if (count >= items.size) {
            maxValue = (count * items.size) % Integer.MAX_VALUE
            notifyDataSetChanged()
        }
    }

    /**
     * Override the [onBindViewHolder] from [RecyclerView.Adapter] in order to provide the click listener to users.
     * and position of views in recycler view.
     * */
    final override fun onBindViewHolder(holder: VH, position: Int) {
        holder.apply {
            itemView.setOnClickListener {
                onCarouselClickListener?.onClickItem(getActualPosition(position))
            }
            onBindView(this, getActualPosition(position))
        }
    }

    /**
     * This method returns the K digit number divisible by item counts.
     * */
    private fun kDigitNumberDivisibleBySize(divisor: Int, k: Int): Double {
        val max = 10.0.pow(k) - 1
        return max - max % divisor
    }
}
