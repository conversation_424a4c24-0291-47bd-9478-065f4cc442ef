package com.suryadigital.leo.libui.viewpager

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.annotation.ColorInt
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.suryadigital.leo.libui.R
import kotlinx.coroutines.CompletableJob
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Implements a custom viewpager component with auto scrolling behaviour and page indicator
 * by using [ViewPager] and [TabLayout].
 *
 * @attribute autoScroll : Sets the scrolling feature to viewpager
 * @attribute isPreviewEnabled : Sets the preview of previous and next page if it's available
 * @attribute selectedIndicatorColor : Sets the color to current page in the viewpager
 * @attribute hideIndicator : Sets the visiblity of page indicator
 * @attribute scrollerDuration : Sets the custom time duration for page scroller
 * @attribute pageChangeDuration : Sets the page change duration, time interval between two pages
 * @attribute isStoppedWhenCompleted : Stops the viewpager(If scrolling) when it reaches the end,
 * will be helpful to implement help screens
 * */

class ViewPager : FrameLayout {
    private var isAutoScrollable: Boolean = true
    private lateinit var viewPagerAdapter: ViewPagerAdapter
    private lateinit var viewPagerContentView: ViewPagerContentView
    private lateinit var tabLayout: TabLayout
    private var autoScrollJob: CompletableJob? = null
    private var ctx: Context
    private var onClickListener: ViewPagerItemClickListener? = null
    private lateinit var attributes: ViewPagerAttributes

    constructor(context: Context) : super(context) {
        ctx = context
        initView(context, null)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        ctx = context
        initView(context, attrs)
    }

    constructor(
        context: Context,
        attrs: AttributeSet,
        defStyleAttr: Int,
    ) : super(context, attrs, defStyleAttr, 0) {
        ctx = context
        initView(context, attrs)
    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int,
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        ctx = context
        initView(context, attrs)
    }

    private fun initView(
        context: Context,
        attrs: AttributeSet?,
    ) {
        val view = LayoutInflater.from(context).inflate(R.layout.viewpager_layout, this, true)
        viewPagerContentView = view.findViewById(R.id.viewPagerContentView)
        tabLayout = view.findViewById(R.id.tab_layout)
        attributes = ViewPagerAttributes(context, attrs)
        tabLayout.setSelectedTabIndicatorColor(attributes.selectedIndicatorColor)
        setIndicatorVisibility()
        viewPagerContentView.setPagerScrollDuration(attributes.scrollerDuration.toInt())
        viewPagerContentView.enablePreview()
        viewPagerContentView.enableTouchAndHold()
        tabLayout.disableIndicator()
    }

    private fun setIndicatorVisibility() {
        if (attributes.hideIndicator) tabLayout.visibility = View.GONE
    }

    fun setViews(views: List<View>) {
        viewPagerAdapter = ViewPagerAdapter(views)
        viewPagerContentView.adapter = viewPagerAdapter
        tabLayout.setupWithViewPager(viewPagerContentView)
        if (attributes.autoScroll) startAutoScrolling()
    }

    fun setPageClickListener(listener: ViewPagerItemClickListener?) {
        onClickListener = listener
    }

    fun startAutoScrolling() {
        if (isAutoScrollable && autoScrollJob == null) {
            playViewPager()
        }
    }

    fun playViewPager() {
        autoScrollJob = Job()
        CoroutineScope(autoScrollJob!! + Dispatchers.Unconfined).launch {
            while (true) {
                delay(attributes.pageChangeDuration)
                scrollToNextPage()
            }
        }
    }

    private suspend fun scrollToNextPage() {
        withContext(Dispatchers.Main) {
            var nextPage = viewPagerContentView.currentItem + 1
            if (nextPage == viewPagerContentView.adapter!!.count) {
                nextPage = 0
            }
            viewPagerContentView.setCurrentItem(nextPage, true)
            if (attributes.isStoppedWhenCompleted && viewPagerContentView.currentItem == viewPagerContentView.adapter!!.count - 1) {
                isAutoScrollable = false
                stopViewPager()
            }
        }
    }

    private suspend fun scrollTo(index: Int) {
        withContext(Dispatchers.Main) {
            if (index >= viewPagerContentView.adapter!!.count || index < 0) {
                throw IllegalArgumentException("Cannot set $index as current item, Item = ${viewPagerContentView.adapter!!.count}, index = $index")
            }
            viewPagerContentView.setCurrentItem(index, false)
            if (attributes.isStoppedWhenCompleted && viewPagerContentView.currentItem == viewPagerContentView.adapter!!.count - 1) {
                isAutoScrollable = false
                stopViewPager()
            }
        }
    }

    fun isAutoScrolling(): Boolean = autoScrollJob != null && attributes.autoScroll

    /**
     * This method stops the viewpager(if it's scrolling) and if you call this method for restarting you need
     * to call [playViewPager] method. Note [startAutoScrolling] method will not work.
     * */
    fun stopViewPager() {
        isAutoScrollable = false
        autoScrollJob?.cancel()
        autoScrollJob = null
    }

    /**
     * If you want to stop the [viewPagerContentView] for some particular case then call [pauseViewPager], it will auto resume if
     * touch and hold listener is enabled on view pager.
     * */
    fun pauseViewPager() {
        autoScrollJob?.cancel()
        autoScrollJob = null
    }

    fun setScrollDuration(millis: Long) {
        attributes.scrollerDuration = millis
    }

    fun setPageChangeDuration(millis: Long) {
        attributes.pageChangeDuration = millis
    }

    fun stopScrollWhenCompelete() {
        attributes.isStoppedWhenCompleted = true
    }

    fun setSelectedIndicatorColor(@ColorInt color: Int) {
        attributes.selectedIndicatorColor = color
        tabLayout.setSelectedTabIndicatorColor(attributes.selectedIndicatorColor)
    }

    /**
     * A utility function to disable the tab layout which is working as page indicator.
     * */
    private fun TabLayout.disableIndicator() {
        val tabStrip = getChildAt(0) as LinearLayout
        for (i in 0 until tabStrip.childCount) {
            tabStrip.getChildAt(i).setOnTouchListener { view, _ ->
                view.performClick()
                true
            }
        }
    }

    fun setViewPagerPreview(enablePreview: Boolean) {
        attributes.isPreviewEnabled = enablePreview
    }

    /**
     * Returns the current page index from viewpager
     * */
    fun getCurrentIndex(): Int = viewPagerContentView.currentItem

    /**
     * This set the current page to the given index in view pager.
     *
     * @param index : Sets the [ViewPager.setCurrentItem] property of viewpager
     * */
    fun setCurrentIndex(index: Int) {
        CoroutineScope(Dispatchers.Main).launch {
            scrollTo(index)
        }
    }

    /**
     * An extension function which provides the preview of previous and next page in viewpager.
     * */
    private fun ViewPager.enablePreview() {
        if (!attributes.isPreviewEnabled) return
        val previewSize = 48
        setPadding(previewSize, 0, previewSize, 0)
        clipToPadding = false
        pageMargin = 16

        // To remove the extra space at first and last element
        setPageTransformer(false) { page, _ ->
            when (currentItem) {
                0 -> {
                    page.translationX = -previewSize.toFloat()
                }
                adapter!!.count - 1 -> {
                    page.translationX = previewSize.toFloat()
                }
                else -> {
                    page.translationX = 0f
                }
            }
        }
    }

    /**
     * This method enable's the touch and hold page feature, as well as set's [ViewPagerTouchListener] to the [ViewPager]
     * */
    private fun ViewPagerContentView.enableTouchAndHold() {
        setOnTouchListener(object : ViewPagerTouchListener() {
            override fun onDownTouchAction() {
                pauseViewPager()
            }

            override fun onUpTouchAction() {
                startAutoScrolling()
                onClickListener?.onItemClickListener(viewPagerContentView.currentItem)
            }

            override fun onCancelTouchAction() {
                startAutoScrolling()
            }
        })
    }
}

/**
 * An item click listener that can be called on [ViewPager] and it will return the current index tapped.
 * */
interface ViewPagerItemClickListener {
    fun onItemClickListener(index: Int)
}
