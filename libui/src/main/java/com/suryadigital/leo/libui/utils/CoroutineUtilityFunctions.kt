package com.suryadigital.leo.libui.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal fun launchOnMainDispatcher(block: suspend () -> Unit) {
    CoroutineScope(Dispatchers.Main).launch {
        block()
    }
}

internal fun launchOnMainDispatcher(block: () -> Unit) {
    CoroutineScope(Dispatchers.Main).launch {
        block()
    }
}
