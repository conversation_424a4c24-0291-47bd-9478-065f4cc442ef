package com.suryadigital.leo.libui.contactpicker

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.suryadigital.leo.libui.R
import java.lang.IllegalStateException

internal class DefaultContactListAdapter(
    private val context: Context,
) : ContactListAdapter(context) {
    override fun getSelectedContactIcon(): Int = R.mipmap.selected_tick_ic

    override fun getDefaultContactIcon(): Int = R.drawable.ic_person_black_24dp

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup?,
    ): View {
        val rowView: View?
        val holder: ContactViewHolder
        if (convertView == null) {
            rowView =
                LayoutInflater.from(context).inflate(R.layout.contact_list_item, parent, false)
            if (rowView != null) {
                holder = ContactViewHolder(rowView)
            } else {
                throw IllegalStateException("Inflated contact item view is null")
            }
            rowView.tag = holder
        } else {
            rowView = convertView
            holder = rowView.tag as ContactViewHolder
        }
        with(holder) {
            name.text = filterContactList[position].name
            phone.text = filterContactList[position].contactNumber
            setSelectionIconIfItemIsSelected(profileImage, position)
        }
        return rowView
    }

    class ContactViewHolder(
        view: View,
    ) {
        val name: TextView = view.findViewById(R.id.contact_name)
        val phone: TextView = view.findViewById(R.id.mobile_number)
        val profileImage: ImageView = view.findViewById(R.id.contact_image)
    }
}
