package com.suryadigital.leo.libui.otptextfield

import android.app.PendingIntent
import android.content.Intent

interface AutoOTPReadListener {
    /**
     * This method will called when [SmsBroadcastReceiver] will successfully retrieve the message from system SMS.
     * This message is given to you as-is in 'message'. Let's assume the SMS received by the user is "Your OTP is #23489"
     * then this method is called with 'message' set to the aforementioned SMS string.
     * */
    fun onSuccess(message: String)

    /**
     * In case of timeout or runtime exceptions this method will be called.
     * */
    fun onFailure(e: OTPFailureException)

    /**
     * When user consent OTP read feature will be requested this method will be called, and provides [Intent]
     * and request code, the pending intent will be used to start the consent activity using from Activity/Fragment and
     * handle the corresponding callbacks. check the link for details on how to use the intent
     * https://developers.google.com/identity/sms-retriever/user-consent/request#2_start_listening_for_incoming_messages
     * */
    fun onConsentIntentGenerated(
        intent: Intent,
        requestCode: Int,
    )

    /**
     * When mobile number hint picker is enabled, this method will be called and provides the [PendingIntent] and
     * request code, the intent will be used to show the mobile hint picker dialog from Activity/Fragment and
     * handle the corresponding callbacks. check the link for details on how to use the intent
     * https://developers.google.com/identity/sms-retriever/request#1_obtain_the_users_phone_number
     * */
    fun onMobileHintPickerIntentGenerated(
        intent: PendingIntent,
        requestCode: Int,
    )

    /**
     * In case of SmsRetrieverClient fails to complete startSmsUserConsent or startSmsRetriever task this method will be called.
     * These APIs are handled by Google and can be updated by them remotely. Currently[7-April-2023] the APIs are not functional for non google pixel devices.
     * https://issuetracker.google.com/issues/275036593
     */
    fun onAutoReadOTPFailed(e: OTPFailureException)
}
