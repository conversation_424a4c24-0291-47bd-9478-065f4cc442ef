package com.suryadigital.leo.libui.carouselview

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import com.suryadigital.leo.libui.R

internal class CarouselAttributes(context: Context, attrs: AttributeSet?) {

    companion object {
        @JvmStatic internal val SELECTED_INDICATOR_RADIUS = 8f

        @JvmStatic internal val UNSELECTED_INDICATOR_RADIUS = 7f

        @JvmStatic internal val SELECTED_INDICATOR_COLOR = Color.BLACK

        @JvmStatic internal val UNSELECTED_INDICATOR_COLOR = Color.GRAY

        @JvmStatic internal val PAGE_CHANGE_DURATION = 3000

        @JvmStatic internal val INDICATOR_VISIBLE = false
    }

    val selectedIndicatorRadius: Float
    val pageChangeDuration: Long
    val unSelectedIndicatorRadius: Float
    val selectedIndicatorColor: Int
    val unSelectedIndicatorColor: Int
    var isIndicatorVisible: Boolean

    init {
        val attributes = context.obtainStyledAttributes(
            attrs,
            R.styleable.CarouselRecyclerView,
            0,
            0,
        )
        try {
            pageChangeDuration =
                attributes.getInt(
                    R.styleable.CarouselRecyclerView_carouselPageChangeDuration,
                    PAGE_CHANGE_DURATION,
                ).toLong()

            selectedIndicatorRadius = attributes.getFloat(
                R.styleable.CarouselRecyclerView_selectedIndicatorRadius,
                SELECTED_INDICATOR_RADIUS,
            )

            unSelectedIndicatorRadius = attributes.getFloat(
                R.styleable.CarouselRecyclerView_unSelectedIndicatorRadius,
                UNSELECTED_INDICATOR_RADIUS,
            )

            selectedIndicatorColor = attributes.getColor(
                R.styleable.CarouselRecyclerView_carouselSelectedIndicatorColor,
                SELECTED_INDICATOR_COLOR,
            )

            unSelectedIndicatorColor = attributes.getColor(
                R.styleable.CarouselRecyclerView_unselectedIndicatorColor,
                UNSELECTED_INDICATOR_COLOR,
            )

            isIndicatorVisible = attributes.getBoolean(
                R.styleable.CarouselRecyclerView_indicatorVisible,
                INDICATOR_VISIBLE,
            )
        } finally {
            attributes.recycle()
        }
    }
}
