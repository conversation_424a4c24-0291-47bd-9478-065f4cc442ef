package com.suryadigital.leo.libui.carouselview

import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.annotation.ColorInt
import androidx.core.view.marginBottom
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import androidx.recyclerview.widget.SnapHelper
import kotlin.math.max

// https://stackoverflow.com/a/48071124/5163725
internal class CirclePagerIndicatorDecoration(
    private val pageCount: Int,
    private val snapHelper: SnapHelper,
) : ItemDecoration() {

    private var selectedColor = Color.BLACK
    private var defaultColor = Color.GRAY
    private var selectedRadius = (DP * 12)
    private var defaultRadius = (DP * 12)
    private val mIndicatorItemLength = DP * 4
    private val mIndicatorItemPadding = DP * 8
    private val mPaint by lazy(::Paint)
    override fun onDrawOver(
        c: Canvas,
        parent: RecyclerView,
        state: RecyclerView.State,
    ) {
        super.onDrawOver(c, parent, state)

        if (pageCount == 0 || pageCount == 1) return
        val totalLength = mIndicatorItemLength * pageCount
        val paddingBetweenItems = max(0, pageCount - 1) * mIndicatorItemPadding
        val indicatorTotalWidth = totalLength + paddingBetweenItems
        val indicatorStartX = (parent.width - indicatorTotalWidth) / 2f

        // Find active page (which should be highlighted) using snap helper, as findLastVisiblePosition
        // may return -1 if nothing is visible.
        val layoutManager = parent.layoutManager as LinearLayoutManager?
        val activePosition = getSnapPosition(layoutManager)
        val actualActivePosition = activePosition % pageCount

        // find offset of active page (if the user is scrolling)
        layoutManager!!.findViewByPosition(activePosition)?.let {
            val indicatorPosY = parent.height - selectedRadius * 2f - it.marginBottom - 16
            for (i in 0 until pageCount) {
                drawIndicators(i, actualActivePosition, c, indicatorStartX, indicatorPosY)
            }
        }
    }

    /**
     * This method will provide the snap position of recyclerview.
     * @param layoutManager : Recycler view layout mangaer which defines the order in
     * which recycler view items needs to be displayed.
     * */
    private fun getSnapPosition(layoutManager: RecyclerView.LayoutManager?): Int {
        if (layoutManager == null) {
            return RecyclerView.NO_POSITION
        }
        val snapView: View = snapHelper.findSnapView(layoutManager)
            ?: return RecyclerView.NO_POSITION
        return layoutManager.getPosition(snapView)
    }

    private fun drawIndicators(
        i: Int,
        actualActivePosition: Int,
        c: Canvas,
        indicatorStartX: Float,
        indicatorPosY: Float,
    ) {
        var radius = defaultRadius
        if (i == actualActivePosition) {
            radius = selectedRadius
            mPaint.color = selectedColor
        } else {
            mPaint.color = defaultColor
        }

        c.drawCircle(
            indicatorStartX + i * mIndicatorItemPadding,
            indicatorPosY,
            radius,
            mPaint,
        )
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State,
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        outRect.bottom = selectedRadius.toInt()
    }

    fun setSelectedIndicatorRadius(radius: Float) {
        selectedRadius = radius
    }

    fun setUnelectedIndicatorRadius(radius: Float) {
        defaultRadius = radius
    }

    fun setSelectedIndicatorColor(@ColorInt color: Int) {
        selectedColor = color
    }

    fun setUnselectedIndicatorColor(@ColorInt color: Int) {
        defaultColor = color
    }

    companion object {
        private val DP =
            Resources.getSystem().displayMetrics.density
    }

    init {
        mPaint.style = Paint.Style.FILL
        mPaint.isAntiAlias = true
    }
}
