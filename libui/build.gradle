import java.nio.file.Paths

plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'maven-publish'
    alias(libs.plugins.spotless)
}

android {
    namespace = "com.suryadigital.leo.libui"
    compileSdk = compile_sdk_version.toInteger()
    buildToolsVersion = build_tools_version

    defaultConfig {
        minSdkVersion min_sdk_version.toInteger()
        targetSdkVersion target_sdk_version.toInteger()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lint {
        abortOnError = true
        warningsAsErrors = true
        disable 'GradleDependency',
                'MissingPermission',
                'OldTargetApi',
                'MediaCapabilities',
                'UnusedResources'
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }

    publishing {
        singleVariant("release") {
            withSourcesJar()
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.androidx.appcompat
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.lifecycle.viewmodel
//    implementation libs.androidx.lifecycle.extensions
    implementation libs.androidx.core.ktx
//    implementation libs.google.play.services.vision

    implementation libs.google.play.services.auth
    implementation libs.google.play.services.auth.api.phone
    implementation libs.google.play.services.maps
    implementation libs.coroutines.android
//    implementation libs.androidx.legacy.support
    implementation libs.coil
    api libs.google.android.material
    api libs.hbb20.ccp
    api libs.androidx.recyclerview
    api libs.otp.view
    api libs.ucrop
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.viewpager2
    implementation libs.androidx.camera.lifecycle
    implementation libs.androidx.camera.view
    implementation libs.google.play.services.barcode.scanning
    coreLibraryDesugaring libs.desugar

    testImplementation libs.junit
    testImplementation libs.robolectric
    debugImplementation libs.androidx.fragment.testing
    testImplementation libs.kotlin.test.common
    testImplementation libs.kotlin.test.junit
    androidTestImplementation libs.junit
    androidTestImplementation libs.androidx.test.core
    androidTestImplementation libs.androidx.test.ext.junit
    androidTestImplementation libs.androidx.test.espresso
}

apply from: Paths.get("${project.rootDir}", "java.gradle")
apply from: Paths.get("${project.rootDir}", "spotless.gradle")

def artifactoryUsername = (System.getenv("ARTIFACTORY_USERNAME") ?: null)
def artifactoryPassword = (System.getenv("ARTIFACTORY_PASSWORD") ?: null)

publishing {
    repositories {
        maven {
            url = 'https://artifacts.surya-digital.in/repository/maven-releases/'
            credentials {
                username = artifactoryUsername
                password = artifactoryPassword
            }
        }
    }
}

afterEvaluate {
    publishing {
        publications {
            release(MavenPublication) {
                from components.release
                groupId = 'com.suryadigital.leo'
                artifactId = 'libui'
                version = (System.getenv("NEXT_BUILD_VERSION") ?: 'SNAPSHOT')
            }
        }
    }
}
