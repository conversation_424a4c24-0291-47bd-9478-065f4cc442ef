buildscript {
    repositories {
        google()
        mavenCentral()
        mavenLocal()
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath libs.android.gradle
        classpath libs.kotlin.android
        classpath libs.build.info.extractor.gradle
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        mavenLocal()
        maven { url 'https://jitpack.io' }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}
