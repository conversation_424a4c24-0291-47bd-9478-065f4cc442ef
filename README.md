# Leo-Android-UI
Android UI Components that are part of Leo

## Location Component

To use location component you should have an API key.
As of now we are using Google Maps for this component.

To get and API key:

1. Go to [Google Cloud Platform console](https://console.cloud.google.com/google/maps-apis/overview).
2. Click the project drop-down and select or create the project.
3. Go to the project you have created.
4. Click on Library below Dashboard on the left side bar.
5. Search for Maps SDK for Android and click on it.
6. Enable the Maps SDK for android for your project.
7. Go to [Google Cloud Platform Dashboard](https://console.cloud.google.com/).
8. Click the menu button and select APIs & Services > Credentials.
9. On the Credentials page, click Create credentials > API key.
   The API key created dialog displays your newly created API key.
10. Copy the Key and replace "PUT_YOUR_KEY_HERE" with your key in googlemap.key.
11. Click Close.
   The new API key is listed on the Credentials page under API keys.

For more details: [Google Map Documentation](https://developers.google.com/maps/documentation/android-sdk/get-api-key)
