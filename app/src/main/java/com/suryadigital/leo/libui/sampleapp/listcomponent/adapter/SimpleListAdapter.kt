package com.suryadigital.leo.libui.sampleapp.listcomponent.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.recyclerview.widget.RecyclerView
import com.suryadigital.leo.libui.listview.ListAdapter
import com.suryadigital.leo.libui.sampleapp.R
import com.suryadigital.leo.libui.sampleapp.listcomponent.adapter.viewholder.CategoryViewHolder
import com.suryadigital.leo.libui.sampleapp.listcomponent.adapter.viewholder.DataViewHolder
import com.suryadigital.leo.libui.sampleapp.listcomponent.adapter.viewholder.LoginViewHolder
import com.suryadigital.leo.libui.sampleapp.listcomponent.adapter.viewholder.MoreOptionViewHolder
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.CategoryModel
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.DataItem
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.DataModel
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.LoginModel
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.MoreOptionModel

private const val TYPE_CATEGORY = 1
private const val TYPE_DATA = 2
private const val TYPE_LOGIN = 3
private const val TYPE_MORE_OPTION = 4

class SimpleListAdapter(
    private val itemList: List<DataModel>,
) : ListAdapter<DataModel, RecyclerView.ViewHolder>(itemList) {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder =
        when (viewType) {
            TYPE_DATA -> {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_data, parent, false)
                DataViewHolder(itemView)
            }
            TYPE_CATEGORY -> {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_category, parent, false)
                CategoryViewHolder(itemView)
            }
            TYPE_LOGIN -> {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_login, parent, false)
                LoginViewHolder(itemView)
            }
            TYPE_MORE_OPTION -> {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_more_option, parent, false)
                MoreOptionViewHolder(itemView)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }

    override fun onBindView(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        when (getItemViewType(position)) {
            TYPE_DATA -> bindDataViewHolder(holder as DataViewHolder, filterItemList[position] as DataItem)
            TYPE_CATEGORY -> bindCategoryViewHolder(holder as CategoryViewHolder, filterItemList[position] as CategoryModel)
            TYPE_LOGIN -> bindLoginViewHolder(holder as LoginViewHolder, filterItemList[position] as LoginModel)
            TYPE_MORE_OPTION -> bindMoreOptionViewHolder(holder as MoreOptionViewHolder, filterItemList[position] as MoreOptionModel)
            else -> throw IllegalStateException("Unsupported item type")
        }
    }

    override fun getItemViewType(position: Int): Int =
        when (filterItemList[position]) {
            is DataItem -> TYPE_DATA
            is CategoryModel -> TYPE_CATEGORY
            is LoginModel -> TYPE_LOGIN
            is MoreOptionModel -> TYPE_MORE_OPTION
        }

    private fun bindDataViewHolder(
        holder: DataViewHolder,
        dataItem: DataItem,
    ) {
        holder.name.text = dataItem.name
        if (dataItem.phone.isEmpty()) {
            holder.contact.visibility = View.GONE
        } else {
            holder.contact.visibility = View.VISIBLE
            holder.contact.text = dataItem.phone
        }
        if (dataItem.lastPaidAmount.isEmpty()) {
            holder.lastPaid.visibility = View.GONE
        } else {
            holder.lastPaid.visibility = View.VISIBLE
            holder.lastPaid.text = holder.itemView.context.getString(R.string.lastPaid, dataItem.lastPaidAmount, dataItem.lastPaidDate)
        }
    }

    private fun bindCategoryViewHolder(
        holder: CategoryViewHolder,
        categoryModel: CategoryModel,
    ) {
        holder.categoryName.text = categoryModel.categoryName
    }

    private fun bindLoginViewHolder(
        holder: LoginViewHolder,
        loginModelModel: LoginModel,
    ) {
        val context = holder.itemView.context
        holder.profilePic.setImageResource(loginModelModel.image)
        holder.userName.text = loginModelModel.userName
        holder.btnLogin.setOnClickListener {
            holder.btnLogin.visibility = View.GONE
            holder.btnLogout.visibility = View.VISIBLE
            showToast(context, context.getString(R.string.user_logged_in, loginModelModel.userName))
        }
        holder.btnLogout.setOnClickListener {
            holder.btnLogin.visibility = View.VISIBLE
            holder.btnLogout.visibility = View.GONE
            showToast(context, context.getString(R.string.user_logged_out, loginModelModel.userName))
        }
    }

    private fun bindMoreOptionViewHolder(
        holder: MoreOptionViewHolder,
        moreOptionModel: MoreOptionModel,
    ) {
        val context = holder.itemView.context
        val popupMenu = PopupMenu(context, holder.moreOptions)
        moreOptionModel.options.forEach(popupMenu.menu::add)
        holder.moreOptionTitle.text = moreOptionModel.title
        holder.moreOptions.setOnClickListener {
            popupMenu.show()
        }

        popupMenu.setOnMenuItemClickListener {
            showToast(context, context.getString(R.string.you_selected, it.title))
            true
        }
    }

    /**
     *    To tell the adapter that data is changed, there are mulitple methods such as insertion of an item,
     *    removal, adding etc and notifyDataSetChanged says that all of the data is changed and
     *    so list will be populated from scratch.
     *    Linter is throwing the error saying we should use other methods if possible and this one should
     *    be last resort. But in our case we are updating the complete list at once[When data is fetched
     *    from the server] and so we can't use any other methods.
     *    */
    @SuppressWarnings("NotifyDataSetChanged")
    override fun filter(query: String) {
        filterItemList.clear()
        if (query.isEmpty()) {
            filterItemList.addAll(itemList)
        } else {
            itemList.filterTo(filterItemList) {
                getItemByType(it, query)
            }
        }
        notifyDataSetChanged()
    }

    private fun getItemByType(
        item: DataModel,
        query: String,
    ): Boolean =
        when (item) {
            is DataItem -> item.name.contains(query, true)
            is CategoryModel -> item.categoryName.contains(query, true)
            is LoginModel -> item.userName.contains(query, true)
            is MoreOptionModel -> item.title.contains(query, true)
        }

    private fun showToast(
        context: Context,
        msg: String,
    ) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
    }
}
