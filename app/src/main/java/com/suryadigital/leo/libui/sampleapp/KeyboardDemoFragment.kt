package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.numberkeyboard.NumberKeyboardView

class KeyboardDemoFragment :
    Fragment(R.layout.frament_keyboard_demo),
    NumberKeyboardView.InputListener {
    private lateinit var keyboardView: NumberKeyboardView
    private lateinit var outputTV: TextView

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
    }

    private fun initViews(view: View) {
        view.apply {
            keyboardView = findViewById(R.id.keyboard_view)
            outputTV = findViewById(R.id.output_tv)
            keyboardView.setup(this@KeyboardDemoFragment, 4)
        }
    }

    override fun onInputTap(num: String) {
        val output = "MWK $num"
        outputTV.text = output
    }
}
