package com.suryadigital.leo.libui.sampleapp.listcomponent

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.listview.ListAdapter
import com.suryadigital.leo.libui.listview.ListRecyclerView
import com.suryadigital.leo.libui.sampleapp.R
import com.suryadigital.leo.libui.sampleapp.listcomponent.adapter.SimpleListAdapter
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.CategoryModel
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.DataItem
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.LoginModel
import com.suryadigital.leo.libui.sampleapp.listcomponent.model.MoreOptionModel

class ListComponentDemo :
    Fragment(R.layout.fragment_list_component),
    ListAdapter.OnItemClickListener {
    private val dataList by lazy {
        listOf(
            CategoryModel(getString(R.string.button_example)),
            LoginModel(R.drawable.ic_person, getString(R.string.demo_user_1)),
            CategoryModel(getString(R.string.multi_textview_example)),
            DataItem(
                getString(R.string.demo_user_2),
                getString(R.string.demo_contact_text),
                getString(R.string.demo_last_paid_amount_text),
                getString(R.string.demo_last_paid_date_text),
            ),
            CategoryModel(getString(R.string.more_option_example)),
            MoreOptionModel(getString(R.string.click_for_more_options), listOf(getString(R.string.edit), getString(R.string.delete))),
        )
    }
    private val adapter by lazy { SimpleListAdapter(dataList) }
    private lateinit var searchET: EditText
    private lateinit var recyclerView: ListRecyclerView

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        searchET = view.findViewById(R.id.etSearch)
        recyclerView = view.findViewById(R.id.listRecyclerView)
        adapter.setOnItemClickListener(this)
        searchET.addTextChangedListener(FilterTextWatcher())
        recyclerView.adapter = adapter
    }

    inner class FilterTextWatcher : TextWatcher {
        override fun beforeTextChanged(
            s: CharSequence,
            start: Int,
            count: Int,
            after: Int,
        ) {
        }

        override fun onTextChanged(
            s: CharSequence,
            start: Int,
            before: Int,
            count: Int,
        ) {
            adapter.filter(searchET.text.toString())
        }

        override fun afterTextChanged(s: Editable) {
        }
    }

    override fun onItemClicked(pos: Int) {
        val title =
            when (val data = dataList[pos]) {
                is CategoryModel -> data.categoryName
                is DataItem -> data.name
                is LoginModel -> data.userName
                is MoreOptionModel -> data.title
            }
        Toast.makeText(requireContext(), getString(R.string.you_selected, title), Toast.LENGTH_SHORT).show()
    }
}
