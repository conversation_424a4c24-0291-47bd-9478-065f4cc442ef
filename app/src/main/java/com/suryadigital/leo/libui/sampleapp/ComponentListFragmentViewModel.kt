package com.suryadigital.leo.libui.sampleapp

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import com.suryadigital.leo.libui.sampleapp.contactpicker.ContactPickerDemo
import com.suryadigital.leo.libui.sampleapp.listcomponent.ListComponentDemo
import com.suryadigital.leo.libui.sampleapp.textdropdowndemo.TextDropdownDemo

class ComponentListFragmentViewModel(
    context: Context,
) : ViewModel() {
    val components: MutableList<Pair<String, () -> Fragment>> = mutableListOf()

    init {
        // Add the new component created to the list
        components.add(context.getString(R.string.country_picker_fragment) to ::CountryPickerFragment)
        components.add(context.getString(R.string.image_picker) to ::ImagePickerFragment)
        components.add(context.getString(R.string.vertical_image_button_fragment) to ::VerticalImageButtonDemoFragment)
        components.add(context.getString(R.string.view_pager_fragment) to ::ViewPagerDemoFragment)
        components.add(context.getString(R.string.carousel_view) to ::CarouselViewDemoFragment)
        components.add(context.getString(R.string.otp_text_field) to ::OtpTextFieldFragment)
        components.add(context.getString(R.string.otp_with_consent) to ::OtpWithConsentFragment)
        components.add(context.getString(R.string.barcode) to ::BarcodeDemoFragment)
        components.add(context.getString(R.string.date_picker) to ::DatePickerDemo)
        components.add(context.getString(R.string.password_text_field) to ::PasswordFieldDemo)
        components.add(context.getString(R.string.progress_button) to ::ProgressButtonDemo)
        components.add(context.getString(R.string.text_dropdown) to ::TextDropdownDemo)
        components.add(context.getString(R.string.contact_picker) to ::ContactPickerDemo)
        components.add(context.getString(R.string.location) to ::LocationComponentFragment)
        components.add(context.getString(R.string.list_component) to ::ListComponentDemo)
        components.add(context.getString(R.string.contact_view_demo) to ::ContactImageViewDemo)
        components.add(context.getString(R.string.landing_screen_demo) to ::LandingScreenDemoFragment)
        components.add(context.getString(R.string.keyboard_demo) to ::KeyboardDemoFragment)
    }
}
