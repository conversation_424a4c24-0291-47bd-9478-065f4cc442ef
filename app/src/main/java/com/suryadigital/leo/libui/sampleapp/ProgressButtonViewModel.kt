package com.suryadigital.leo.libui.sampleapp

import androidx.lifecycle.ViewModel

class ProgressButtonViewModel : ViewModel() {
    var isLoginProgressEnabled: Boolean = false
    var isSigninProgressEnabled: Boolean = false
    var isDownloadProgressEnabled: Boolean = false
    var isSubscribeProgressEnabled: Boolean = false
    var isSubmitProgressEnabled: Boolean = false
    var isOKProgressEnabled: Boolean = false
}
