package com.suryadigital.leo.libui.sampleapp.listcomponent.adapter.viewholder

import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.suryadigital.leo.libui.sampleapp.R

class DataViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    val name: TextView = itemView.findViewById(R.id.name)
    val contact: TextView = itemView.findViewById(R.id.contact)
    val lastPaid: TextView = itemView.findViewById(R.id.lastPaid)
}
