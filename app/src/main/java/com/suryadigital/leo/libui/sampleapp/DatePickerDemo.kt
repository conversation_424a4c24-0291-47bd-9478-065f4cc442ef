package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.datepicker.DatePicker
import java.time.LocalDate

class DatePickerDemo : Fragment(R.layout.fragment_date_picker_demo) {

    private lateinit var datePicker: DatePicker
    private lateinit var chooseDateButton: Button
    private lateinit var redThemeButton: Button
    private lateinit var greenThemeButton: Button
    private lateinit var dateTV: TextView

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        retainInstance = true
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        chooseDateButton = view.findViewById(R.id.choose_date_btn)
        redThemeButton = view.findViewById(R.id.red_theme_btn)
        greenThemeButton = view.findViewById(R.id.green_theme_btn)
        dateTV = view.findViewById(R.id.date_tv)

        datePicker = DatePicker()
        val dateFormatString = getString(R.string.tv_date_format)
        dateTV.text = String.format(dateFormatString, datePicker.currentDate())

        datePicker.setDateChangeListener(object : DatePicker.OnDateChangeListener {
            override fun onDateChanged(date: LocalDate) {
                dateTV.text = String.format(dateFormatString, "$date")
            }

            override fun dialogCancelled() {
                Log.d("Log : ", "Date dialog cancelled")
            }
        })

        chooseDateButton.setOnClickListener {
            datePicker.show(childFragmentManager, "date_picker")
        }

        redThemeButton.setOnClickListener {
            datePicker.setDatePickerTheme(R.style.RedDatePickerDialogTheme)
            Toast.makeText(requireContext(), getString(R.string.red_theme_applied), Toast.LENGTH_SHORT)
                .show()
        }

        greenThemeButton.setOnClickListener {
            datePicker.setDatePickerTheme(R.style.GreenDatePickerDialogTheme)
            Toast.makeText(requireContext(), getString(R.string.green_theme_applied), Toast.LENGTH_SHORT)
                .show()
        }
    }
}
