package com.suryadigital.leo.libui.sampleapp

import android.app.Activity
import android.app.PendingIntent
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.suryadigital.leo.libui.otptextfield.AutoOTPReadListener
import com.suryadigital.leo.libui.otptextfield.OTPConfiguration
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import com.suryadigital.leo.libui.otptextfield.OTPTextField
import com.suryadigital.leo.libui.otptextfield.OTPTextFieldListener
import com.suryadigital.leo.libui.otptextfield.OtpReadType
import com.suryadigital.leo.libui.otptextfield.parseFirstNdigitOTPNumberInMessage

class OtpWithConsentFragment : Fragment(R.layout.fragment_otp_with_consent) {
    private lateinit var otpView: OTPTextField
    private lateinit var resendButton: Button

    companion object {
        private var smsConsentRequest = 0
        private val TAG = OtpWithConsentFragment::class.qualifiedName
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)

        otpView = view.findViewById(R.id.otpView)
        resendButton = view.findViewById(R.id.resendBtn)

        otpView.otpListener =
            object : OTPTextFieldListener {
                override fun onOTPComplete(otp: String) {
                    Toast.makeText(requireContext(), otp, Toast.LENGTH_SHORT).show()
                }

                override fun onInteractionListener() {
                }
            }

        resendButton.setOnClickListener {
            otpView.otp = ""
        }

        // To configure auto read with user consent
        val otpConfig =
            OTPConfiguration(
                otpReadType = OtpReadType.AUTO_READ_WITH_CONSENT_AND_MANUAL_ENTRY,
            )
        with(otpView) {
            setConfiguration(otpConfig)
            setAutoReadOtpListener(
                autoOTPReadListener =
                    object : AutoOTPReadListener {
                        override fun onSuccess(message: String) {
                            Log.d(TAG, context.getString(R.string.auto_read_successful))
                        }

                        override fun onFailure(e: OTPFailureException) {
                            Log.d(TAG, context.getString(R.string.auto_read_failed))
                        }

                        override fun onConsentIntentGenerated(
                            intent: Intent,
                            requestCode: Int,
                        ) {
                            if (isAdded) {
                                smsConsentRequest = requestCode
                                startActivityForResult(intent, requestCode)
                            }
                        }

                        override fun onMobileHintPickerIntentGenerated(
                            intent: PendingIntent,
                            requestCode: Int,
                        ) {
                            Log.d(TAG, context.getString(R.string.mobile_hint_generated_text))
                        }

                        override fun onAutoReadOTPFailed(e: OTPFailureException) {
                            Log.e(
                                TAG,
                                "SmsRetrieverClient failed to complete sms user consent or sms retriever task",
                                e,
                            )
                        }
                    },
            )
            startOtpReading()
        }
    }

    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            smsConsentRequest ->
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val message = data.getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE)
                    val otpValue = parseFirstNdigitOTPNumberInMessage(message!!, 6)
                    if (!otpValue.isNullOrEmpty()) {
                        otpView.otp = otpValue
                    } else {
                        Toast
                            .makeText(
                                requireContext(),
                                getString(R.string.otp_not_found),
                                Toast.LENGTH_SHORT,
                            ).show()
                    }
                    Log.d(TAG, message) // parse the message to get OTP
                } else {
                    Log.d(TAG, getString(R.string.consent_denied))
                }
        }
    }
}
