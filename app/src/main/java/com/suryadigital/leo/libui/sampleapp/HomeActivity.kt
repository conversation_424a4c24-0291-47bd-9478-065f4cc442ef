package com.suryadigital.leo.libui.sampleapp

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.fragment.app.DialogFragment
import com.suryadigital.leo.libui.contactpicker.ContactPicker
import com.suryadigital.leo.libui.sampleapp.pref.PreferenceHelper
import java.util.Locale

class HomeActivity :
    AppCompatActivity(),
    PermissionDialog.PermissionDialogListener {
    private lateinit var toolbar: Toolbar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_home)
        toolbar = findViewById(R.id.toolbar)
        setSupportActionBar(toolbar)
    }

    fun requestCameraPermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.CAMERA),
            REQUEST_CAMERA_PERMISSION,
        )
    }

    fun requestContactPermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.READ_CONTACTS),
            REQUEST_READ_CONTACTS,
        )
    }

    private fun showContactPermissionRationaleDialog() {
        val permissionDialog = PermissionDialog()
        permissionDialog.show(supportFragmentManager, PermissionDialog.TAG)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_CAMERA_PERMISSION ->
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, resources.getString(R.string.camera_permission_granted))
                } else {
                    Toast
                        .makeText(
                            this,
                            getString(com.suryadigital.leo.libui.R.string.permission_denied),
                            Toast.LENGTH_SHORT,
                        ).show()
                }
            REQUEST_READ_CONTACTS ->
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, resources.getString(R.string.camera_permission_granted))
                    /***
                     * Contact Picker is getting called from SigleContactPickerFragment and MultiplePickerFragment
                     * so in supportFragmentManager the position of Contact Picker is always at index 1.
                     */
                    val fragment = supportFragmentManager.fragments[1]
                    if (fragment is ContactPicker) {
                        fragment.initAdapter()
                    }
                } else if (!shouldShowRequestPermissionRationale(Manifest.permission.READ_CONTACTS)) {
                    showContactPermissionRationaleDialog()
                } else {
                    Toast
                        .makeText(
                            this,
                            getString(com.suryadigital.leo.libui.R.string.permission_denied),
                            Toast.LENGTH_SHORT,
                        ).show()
                }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
            }
            R.id.localeEnglish -> {
                PreferenceHelper.saveLocale(applicationContext, "en")
                resetActivity()
            }
            R.id.localeNyanja -> {
                PreferenceHelper.saveLocale(applicationContext, "ny")
                resetActivity()
            }
        }
        return true
    }

    override fun attachBaseContext(newBase: Context) {
        super.attachBaseContext(
            changeLocale(
                newBase,
                PreferenceHelper.getLocale(newBase.applicationContext, "en"),
            ),
        )
    }

    companion object {
        private const val REQUEST_CAMERA_PERMISSION = 201
        private const val REQUEST_READ_CONTACTS = 301
        private val TAG = HomeActivity::class.java.simpleName
    }

    private fun changeLocale(
        context: Context,
        localeCode: String,
    ): Context {
        val locale = Locale(localeCode)
        val config = context.resources.configuration
        Locale.setDefault(locale)
        PreferenceHelper.saveLocale(context.applicationContext, localeCode)
        config.setLocale(locale)
        return context.createConfigurationContext(config)
    }

    private fun resetActivity() {
        finish()
        startActivity(Intent(this, HomeActivity::class.java))
    }

    private fun openSystemSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri: Uri = Uri.fromParts("package", context.packageName, null)
        intent.data = uri
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        context.startActivity(intent)
    }

    override fun onProceed() {
        openSystemSettings(this)
    }

    override fun onDecline() {
        val frag = supportFragmentManager.findFragmentByTag(PermissionDialog.TAG)
        if (frag is DialogFragment && frag.isVisible) {
            frag.dismiss()
        } else {
            Log.i(PermissionDialog.TAG, "Trying to dismiss dialog when it is not showing.")
        }
    }
}
