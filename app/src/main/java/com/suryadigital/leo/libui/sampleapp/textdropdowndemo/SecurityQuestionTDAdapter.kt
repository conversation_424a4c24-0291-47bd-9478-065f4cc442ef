package com.suryadigital.leo.libui.sampleapp.textdropdowndemo

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.suryadigital.leo.libui.sampleapp.R
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter

class SecurityQuestionTDAdapter(
    private val items: List<String>,
) : AbstractTextDropDownAdapter<String>(items) {
    override fun getDropDownView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_dropdown_item, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as ViewHolder
        }
        viewHolder.apply {
            question.text = items[position]
        }
        return view
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            view?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            question.text = items[position]
        }
        return view
    }

    private class ViewHolder(
        view: View,
    ) {
        var question: TextView =
            view.findViewById(
                R.id.dropdown_item_text_view,
            )
    }
}
