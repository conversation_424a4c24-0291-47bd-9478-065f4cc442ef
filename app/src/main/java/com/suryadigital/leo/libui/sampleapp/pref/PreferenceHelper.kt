package com.suryadigital.leo.libui.sampleapp.pref

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.preference.PreferenceManager

object PreferenceHelper {
    private fun getPreference(appContext: Context): SharedPreferences = PreferenceManager.getDefaultSharedPreferences(appContext)

    fun saveLocale(
        appContext: Context,
        localeCode: String,
    ) {
        getPreference(appContext).edit { putString(LOCALE_CODE, localeCode) }
    }

    fun getLocale(
        appContext: Context,
        defaultLocale: String,
    ): String = getPreference(appContext).getString(LOCALE_CODE, defaultLocale) ?: defaultLocale
}

private const val LOCALE_CODE = "localeCode"
