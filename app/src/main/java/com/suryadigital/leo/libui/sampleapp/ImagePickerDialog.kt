package com.suryadigital.leo.libui.sampleapp

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.DialogFragment
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted

class ImagePickerDialog : DialogFragment() {

    interface ImagePickerListener {
        fun captureImageFromCamera()
        fun pickImageFromGallery()
    }

    private lateinit var openCameraTV: TextView
    private lateinit var openGalleryTV: TextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.dialog_image_picker_dialog, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(rootView)
        return rootView
    }

    private fun initViews(rootView: View) {
        openCameraTV = rootView.findViewById(R.id.take_a_picture)
        openGalleryTV = rootView.findViewById(R.id.select_from_gallery)

        openCameraTV.setOnClickListener {
            val parent = (requireActivity() as HomeActivity)
            if (!checkCameraPermissionGranted(parent)) {
                parent.requestCameraPermission()
            } else {
                (parentFragment as ImagePickerListener).captureImageFromCamera()
            }
            dismiss()
        }
        openGalleryTV.setOnClickListener {
            (parentFragment as ImagePickerListener).pickImageFromGallery()
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        /*
            Maintaining the width of this dialog to 85% of device's width.
         */
        val calculatedWidth = (requireContext().resources.displayMetrics.widthPixels * 85) / 100
        val dialogWidth =
            if (calculatedWidth > FIXED_DIALOG_WIDTH) FIXED_DIALOG_WIDTH else calculatedWidth
        val dialogHeight = LinearLayout.LayoutParams.WRAP_CONTENT

        dialog?.window?.setLayout(dialogWidth, dialogHeight)
    }

    companion object {
        const val TAG: String = "ImagePickerDialog"
    }
}

private const val FIXED_DIALOG_WIDTH = 1224
