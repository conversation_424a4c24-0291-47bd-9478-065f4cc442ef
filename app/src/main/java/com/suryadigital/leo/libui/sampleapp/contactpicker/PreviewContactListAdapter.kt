package com.suryadigital.leo.libui.sampleapp.contactpicker

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import androidx.core.net.toUri
import com.suryadigital.leo.libui.contactpicker.Contact
import com.suryadigital.leo.libui.sampleapp.R
import java.lang.IllegalStateException
import com.suryadigital.leo.libui.R as LibUIResource

class PreviewContactListAdapter(
    private val context: Context,
    private val contactList: ArrayList<Contact>,
) : BaseAdapter() {
    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val rowView: View?
        val holder: ContactViewHolder
        if (convertView == null) {
            rowView =
                LayoutInflater.from(context).inflate(R.layout.contact_demo_list_item, parent, false)
            if (rowView != null) {
                holder = ContactViewHolder(rowView)
            } else {
                throw IllegalStateException("Inflated demo contact list item view is null")
            }
            rowView.tag = holder
        } else {
            rowView = convertView
            holder = rowView.tag as ContactViewHolder
        }
        with(holder) {
            name.text = contactList.elementAt(position).name
            phone.text = contactList.elementAt(position).contactNumber
            val imageUri = contactList.elementAt(position).imageUri
            if (imageUri != null) {
                profileImage.setImageURI(imageUri.toUri())
            } else {
                profileImage.setImageResource(LibUIResource.drawable.ic_person_black_24dp)
            }
        }
        return rowView
    }

    override fun getItem(position: Int): Contact {
        return contactList.elementAt(position)
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getCount(): Int {
        return contactList.size
    }

    class ContactViewHolder(view: View) {
        val name: TextView = view.findViewById(R.id.contact_name)
        val phone: TextView = view.findViewById(R.id.mobile_number)
        val profileImage: ImageView = view.findViewById(R.id.contact_image)
    }
}
