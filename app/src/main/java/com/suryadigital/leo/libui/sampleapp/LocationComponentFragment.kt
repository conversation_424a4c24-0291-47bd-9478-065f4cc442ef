package com.suryadigital.leo.libui.sampleapp

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.Button
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.location.MapFragment
import com.suryadigital.leo.libui.location.model.IntSource
import com.suryadigital.leo.libui.location.model.Location
import com.suryadigital.leo.libui.location.model.StringSource

class LocationComponentFragment :
    Fragment(R.layout.fragment_location_component),
    MapFragment.OnMarkerClickListener,
    MapFragment.ImageLoadListener {
    private lateinit var mapFragment: MapFragment
    private lateinit var goToButton: Button
    private val listOfPlaces by lazy {
        arrayListOf(
            Location(getString(R.string.hospital), 12.929026, 77.599551, IntSource(R.drawable.ic_hospital)),
            Location(getString(R.string.local_bar), 12.935708, 77.614174, IntSource(R.drawable.ic_local_bar)),
            Location(getString(R.string.temple), 12.878079, 77.599341, null),
        )
    }
    private var handler = Handler(Looper.getMainLooper())

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        mapFragment = childFragmentManager.findFragmentById(R.id.map) as MapFragment
        goToButton = view.findViewById(R.id.go_to_location)
        goToButton.setOnClickListener {
            mapFragment.setBoundWithMarkerAndZoomLevel(
                Location("", 12.979284567846527, 77.753276978853, IntSource(R.drawable.ic_hospital)),
                MapFragment.ZoomLevel.STREETS,
            )
            mapFragment.setBoundWithMarkerAndZoomLevel(
                Location("", 12.979914567846527, 77.753276978853, IntSource(R.drawable.ic_local_bar)),
                MapFragment.ZoomLevel.STREETS,
            )
        }
    }

    override fun onResume() {
        super.onResume()

        mapFragment.setLocations(listOfPlaces, true)
        mapFragment.setDefaultMarkerImage(IntSource(R.drawable.ic_location_defult))
        mapFragment.setOnMarkerClickListener(this)
        mapFragment.setOnImageLoadListener(this)

        handler.postDelayed({
            /*
                Demonstrate usage of adding new locations assuming that there are some locations
                already there on map.
             */
            listOfPlaces.add(Location(getString(R.string.restaurant), 12.917047, 77.585435, IntSource(R.drawable.ic_food)))
            listOfPlaces.add(Location(getString(R.string.lake), 12.906735, 77.616121, null))
            listOfPlaces.add(
                Location(
                    getString(R.string.surya_software),
                    12.920648,
                    77.575906,
                    StringSource("https://www.surya-digital.com/wp-content/themes/surya-digital/images/icon2.png"),
                ),
            )
            mapFragment.setLocations(listOfPlaces, true)
        }, 5000)
    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacksAndMessages(null)
    }

    override fun onMarkerClick(index: Int) {
        if (index != -1) {
            val location = listOfPlaces[index]
            Toast
                .makeText(
                    requireContext(),
                    getString(R.string.marker_selected, location.title),
                    Toast.LENGTH_SHORT,
                ).show()
        }
    }

    override fun onImageLoadError(drawable: Drawable?) {
        Toast
            .makeText(
                requireContext(),
                getString(R.string.image_load_error),
                Toast.LENGTH_SHORT,
            ).show()
    }
}
