package com.suryadigital.leo.libui.sampleapp

import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.suryadigital.leo.libui.carouselview.CarouselRecyclerView
import com.suryadigital.leo.libui.carouselview.OnCarouselTouchListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CarouselViewDemoFragment : Fragment(R.layout.fragment_carousel_view_demo) {

    private lateinit var swipeToRefresh: SwipeRefreshLayout
    private lateinit var carouselRecyclerView: CarouselRecyclerView
    private lateinit var carouselRecyclerViewMiddle: CarouselRecyclerView
    private val carouselTouchListener = object : OnCarouselTouchListener() {
        override fun onDownTouchAction() {
            if (!swipeToRefresh.isRefreshing) {
                swipeToRefresh.isEnabled = false
            }
        }

        override fun onUpTouchAction() {
            if (!swipeToRefresh.isRefreshing) {
                swipeToRefresh.isEnabled = true
            }
        }

        override fun onCancelTouchAction() {
            if (!swipeToRefresh.isRefreshing) {
                swipeToRefresh.isEnabled = true
            }
        }

        override fun onMoveTouchAction() {
            if (!swipeToRefresh.isRefreshing) {
                swipeToRefresh.isEnabled = false
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        swipeToRefresh = view.findViewById(R.id.swipeToRefresh)
        carouselRecyclerView = view.findViewById(R.id.carouselView)
        carouselRecyclerViewMiddle = view.findViewById(R.id.carouselViewMiddle)
        carouselRecyclerView.run {
            setAdapter(CustomCarouselAdapter(context, listOf("one", "two", "three", "four")))
            setSelectedIndicatorColor(Color.RED)
            setUnselectedIndicatorColor(Color.GRAY)
            setSelectedIndicatorRadius(8f)
            setUnselectedIndicatorRadius(7f)
            setOnCarouselItemClickListener(object : CarouselRecyclerView.OnCarouselClickListener {
                override fun onClickItem(pos: Int) {
                    Toast.makeText(requireContext(), getString(R.string.clicked_arg, pos), Toast.LENGTH_SHORT).show()
                }
            })
            setCarouselTouchListener(carouselTouchListener)
            showIndicator()
        }

        carouselRecyclerViewMiddle.run {
            val adapter = CustomCarouselAdapter(context, listOf("one", "two", "three"))
            adapter.setScrollSpace(100)
            setAdapter(adapter)
            playCarousel()
            setSelectedIndicatorColor(Color.MAGENTA)
            setUnselectedIndicatorColor(Color.GRAY)
            setSelectedIndicatorRadius(8f)
            setUnselectedIndicatorRadius(7f)
            setOnCarouselItemClickListener(object : CarouselRecyclerView.OnCarouselClickListener {
                override fun onClickItem(pos: Int) {
                    Toast.makeText(requireContext(), getString(R.string.clicked_arg, pos), Toast.LENGTH_SHORT).show()
                }
            })
            setCarouselTouchListener(carouselTouchListener)
            showIndicator()
        }

        swipeToRefresh.setOnRefreshListener {
            CoroutineScope(Dispatchers.IO).launch {
                delay(5000)
                withContext(Dispatchers.Main) {
                    swipeToRefresh.isRefreshing = false
                }
            }
        }
    }
}
