package com.suryadigital.leo.libui.sampleapp.contactpicker

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.ActionBar
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.suryadigital.leo.libui.contactpicker.ContactPicker
import com.suryadigital.leo.libui.contactpicker.ContactPickerViewModel
import com.suryadigital.leo.libui.contactpicker.checkContactPermission
import com.suryadigital.leo.libui.sampleapp.HomeActivity
import com.suryadigital.leo.libui.sampleapp.R

class SingleContactPickerFragment :
    Fragment(R.layout.fragment_layout_contact_picker),
    ContactPicker.PermissionRequestListener {
    private val contactPickerViewModel: ContactPickerViewModel by activityViewModels()
    private var actionBar: ActionBar? = null

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)

        actionBar = (requireActivity() as AppCompatActivity).supportActionBar
        val contactPicker = ContactPicker()
        contactPicker.setPermissionListener(this)
        contactPicker.setArguments(
            contactPickerViewModel = contactPickerViewModel,
            onCompletion = ::onContactSelectionCompleted,
        )
        requireActivity()
            .supportFragmentManager
            .beginTransaction()
            .replace(R.id.contactFragmentContainer, contactPicker)
            .commit()
    }

    private fun onContactSelectionCompleted() {
        requireActivity().onBackPressedDispatcher.onBackPressed()
    }

    override fun requestPermission() {
        val parent = (requireActivity() as HomeActivity)
        if (!checkContactPermission(parent)) {
            parent.requestContactPermission()
        }
    }
}
