package com.suryadigital.leo.libui.sampleapp

import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.imagecrop.ImagePicker

class ImagePickerFragment :
    Fragment(R.layout.fragment_image_picker),
    ImagePickerDialog.ImagePickerListener,
    ImagePicker.ImagePickerFailureListener {
    private lateinit var imageView: ImageView
    private lateinit var pickImageButton: Button
    private lateinit var sizeTV: TextView

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setImagePicker()
    }

    private fun setImagePicker() {
        ImagePicker.setImagePickerFailureListener(this)
        pickImageButton.setOnClickListener {
            val dialog = ImagePickerDialog()
            dialog.show(childFragmentManager, ImagePickerDialog.TAG)
        }
    }

    private fun initViews(view: View) {
        imageView = view.findViewById(R.id.image)
        sizeTV = view.findViewById(R.id.size_tv)
        pickImageButton = view.findViewById(R.id.pick_image_bt)
    }

    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == AppCompatActivity.RESULT_OK) {
            when (requestCode) {
                ImagePicker.IMAGE_PICKER_GALLERY_REQUEST_CODE -> {
                    ImagePicker.cropImage(data, this, compressionQuality = 100, freeStyleCropEnabled = false)
                }
                ImagePicker.IMAGE_PICKER_CAMERA_REQUEST_CODE -> {
                    ImagePicker.cropImage(null, this, compressionQuality = 100, freeStyleCropEnabled = true)
                }
                ImagePicker.IMAGE_CROP_REQUEST_CODE -> {
                    val resultUri = ImagePicker.getOutput(data!!)!!
                    sizeTV.apply {
                        visibility = View.VISIBLE
                        text =
                            getString(
                                R.string.size_of_selected_image_d,
                                requireContext().assetSize(resultUri) / 1024,
                            )
                    }
                    imageView.setImageURI(resultUri)
                }
            }
        }

        if (resultCode == ImagePicker.IMAGE_CROP_RESULT_ERROR_CODE) {
            val cropError = ImagePicker.getError(data!!)
            cropError?.message?.let {
                Log.d(">>>", it)
            }
        }
    }

    private fun Context.assetSize(resourceUri: Uri): Long {
        try {
            val descriptor = contentResolver.openAssetFileDescriptor(resourceUri, "r")
            val size = descriptor?.length ?: return 0
            descriptor.close()
            return size
        } catch (e: Resources.NotFoundException) {
            return 0
        }
    }

    override fun captureImageFromCamera() {
        ImagePicker.openCamera(this)
    }

    override fun pickImageFromGallery() {
        ImagePicker.openImageGallery(this)
    }

    override fun imagePickerFailure(error: Exception) {
        error.message?.let {
            Log.d(">>>", it)
        }
    }
}
