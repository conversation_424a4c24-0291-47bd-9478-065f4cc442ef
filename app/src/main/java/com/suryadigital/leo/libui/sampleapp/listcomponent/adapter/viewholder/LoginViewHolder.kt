package com.suryadigital.leo.libui.sampleapp.listcomponent.adapter.viewholder

import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.suryadigital.leo.libui.sampleapp.R

class LoginViewHolder(
    itemView: View,
) : RecyclerView.ViewHolder(itemView) {
    val profilePic: ImageView = itemView.findViewById(R.id.profilePicture)
    val userName: TextView = itemView.findViewById(R.id.userName)
    val btnLogin: Button = itemView.findViewById(R.id.btnLogin)
    val btnLogout: Button = itemView.findViewById(R.id.btnRegister)
}
