package com.suryadigital.leo.libui.sampleapp.textdropdowndemo

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.suryadigital.leo.libui.sampleapp.R
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter

class AccountTDAdapter(private val items: List<Account>) : AbstractTextDropDownAdapter<Account>(items) {

    override fun getDropDownView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: AccountViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.account_spinner_dropdown_item, parent, false)
            viewHolder =
                AccountViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as AccountViewHolder
        }
        viewHolder.apply {
            accountHolderName.text = items[position].name
            accountNumber.text = items[position].accountNumber
        }
        return view
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            view?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            accountHolderName.text = items[position].name
        }
        return view
    }

    private class AccountViewHolder(view: View) {
        var accountHolderName: TextView = view.findViewById(R.id.account_holder_name)
        var accountNumber: TextView = view.findViewById(R.id.account_number)
    }

    private class ViewHolder(view: View) {
        var accountHolderName: TextView = view.findViewById(R.id.dropdown_item_text_view)
    }
}
