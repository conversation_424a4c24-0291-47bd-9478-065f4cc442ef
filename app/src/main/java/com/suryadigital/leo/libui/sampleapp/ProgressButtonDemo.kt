package com.suryadigital.leo.libui.sampleapp

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.suryadigital.leo.libui.progressbutton.ProgressButton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.CoroutineContext

class ProgressButtonDemo : Fragment(R.layout.layout_progress_button_demo) {

    private val progressButtonViewModel by activityViewModels<ProgressButtonViewModel>()
    private val job = Job()
    private val coroutineContext: CoroutineContext by lazy { job + Dispatchers.IO }

    private lateinit var downloadButton: ProgressButton
    private lateinit var loginButton: ProgressButton
    private lateinit var okButton: ProgressButton
    private lateinit var signinButton: ProgressButton
    private lateinit var submitButton: ProgressButton
    private lateinit var subscribeButton: ProgressButton

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        downloadButton = view.findViewById(R.id.download_button)
        loginButton = view.findViewById(R.id.login_button)
        okButton = view.findViewById(R.id.ok_button)
        signinButton = view.findViewById(R.id.sigin_button)
        submitButton = view.findViewById(R.id.submit_button)
        subscribeButton = view.findViewById(R.id.subscribe_button)

        initializeButtonState()
        with(loginButton) {
            setOnClickListener {
                loginAction()
            }
        }

        with(signinButton) {
            setOnClickListener {
                siginAction()
            }
        }

        with(downloadButton) {
            setOnClickListener {
                downloadAction()
            }
            setText(requireContext().getString(R.string.download))
            setTextColor(Color.GREEN)
            setTextTypeface(Typeface.BOLD_ITALIC)
        }

        with(subscribeButton) {
            setOnClickListener {
                subscribeAction()
            }
        }

        with(submitButton) {
            setOnClickListener {
                submitAction()
            }
        }

        with(okButton) {
            setOnClickListener {
                okAction()
            }
        }
    }

    private fun ProgressButton.action(
        enableProgressStatus: () -> Unit,
        disableProgressStatus: () -> Unit,
        @StringRes messageResId: Int,
    ) {
        showProgressBar()
        enableProgressStatus()
        hideProgressBarView(
            this,
            PROGRESS_DURATION,
            requireContext().getString(messageResId),
            disableProgressStatus,
        )
    }

    private fun hideProgressBarView(
        progressButton: ProgressButton,
        millis: Long,
        action: String,
        disableProgressStatus: () -> Unit,
    ) {
        CoroutineScope(coroutineContext).launch {
            delay(millis)
            withContext(Dispatchers.Main) {
                if (isAdded) {
                    disableProgressStatus()
                    progressButton.hideProgressBar()
                    Toast.makeText(context, action, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun initializeButtonState() {
        if (progressButtonViewModel.isLoginProgressEnabled) {
            loginButton.loginAction()
        }
        if (progressButtonViewModel.isSigninProgressEnabled) {
            signinButton.siginAction()
        }
        if (progressButtonViewModel.isDownloadProgressEnabled) {
            downloadButton.downloadAction()
        }
        if (progressButtonViewModel.isSubscribeProgressEnabled) {
            subscribeButton.subscribeAction()
        }
        if (progressButtonViewModel.isSubmitProgressEnabled) {
            submitButton.submitAction()
        }
        if (progressButtonViewModel.isOKProgressEnabled) {
            okButton.okAction()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        job.cancel()
    }

    private fun ProgressButton.okAction() {
        action(
            { progressButtonViewModel.isOKProgressEnabled = true },
            { progressButtonViewModel.isOKProgressEnabled = false },
            R.string.ok_tap,
        )
    }

    private fun ProgressButton.submitAction() {
        action(
            { progressButtonViewModel.isSubmitProgressEnabled = true },
            { progressButtonViewModel.isSubmitProgressEnabled = false },
            R.string.submit_action,
        )
    }

    private fun ProgressButton.subscribeAction() {
        action(
            { progressButtonViewModel.isSubscribeProgressEnabled = true },
            { progressButtonViewModel.isSubscribeProgressEnabled = false },
            R.string.subscribed,
        )
    }

    private fun ProgressButton.downloadAction() {
        action(
            { progressButtonViewModel.isDownloadProgressEnabled = true },
            { progressButtonViewModel.isDownloadProgressEnabled = false },
            R.string.download_action,
        )
    }

    private fun ProgressButton.siginAction() {
        action(
            { progressButtonViewModel.isSigninProgressEnabled = true },
            { progressButtonViewModel.isSigninProgressEnabled = false },
            R.string.sigin_successfully,
        )
    }

    private fun ProgressButton.loginAction() {
        action(
            { progressButtonViewModel.isLoginProgressEnabled = true },
            { progressButtonViewModel.isLoginProgressEnabled = false },
            R.string.login_successfully,
        )
    }

    companion object {
        private const val PROGRESS_DURATION: Long = 5000
    }
}
