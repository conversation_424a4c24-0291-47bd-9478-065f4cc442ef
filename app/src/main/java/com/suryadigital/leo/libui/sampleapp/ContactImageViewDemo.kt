package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.contactview.ContactIconView
import com.suryadigital.leo.libui.R as LibUIResource

class ContactImageViewDemo : Fragment(R.layout.fragment_contact_image_view_demo) {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Example: Contact with default image
        val contactView = view.findViewById<ContactIconView>(R.id.contactImageView)
        contactView.imageView.setImageResource(LibUIResource.drawable.ic_person_black_24dp)

        val secondName = view.findViewById<TextView>(R.id.second_name_tv)

        // Example: Contact without image
        val secondContactView = view.findViewById<ContactIconView>(R.id.second_contactImageView)
        secondContactView.setContactInitials(secondName.text.toString(), 2)

        // Example: Contact with image
        val thirdContactView = view.findViewById<ContactIconView>(R.id.third_contactImageView)
        thirdContactView.imageView.setImageResource(R.mipmap.demo_contact_image)
        thirdContactView.initialsTextView.visibility = View.GONE
    }
}
