package com.suryadigital.leo.libui.sampleapp.textdropdowndemo

import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.sampleapp.R
import com.suryadigital.leo.libui.textdropdown.TextDropdown

class TextDropdownDemo : Fragment(R.layout.layout_text_dropdown) {

    private lateinit var accountDropDown: TextDropdown
    private lateinit var answerET: EditText
    private lateinit var textDropdown: TextDropdown
    private lateinit var submitButton: Button

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        accountDropDown = view.findViewById(R.id.account_text_dropdown)
        answerET = view.findViewById(R.id.answer_et)
        textDropdown = view.findViewById(R.id.text_dropdown)
        submitButton = view.findViewById(R.id.submit_button)
        val questionList = listOf(
            requireContext().getString(R.string.question1),
            requireContext().getString(R.string.question2),
            requireContext().getString(R.string.question3),
            requireContext().getString(R.string.question4),
            requireContext().getString(R.string.question5),
        )
        textDropdown.setAdapter(
            SecurityQuestionTDAdapter(
                questionList,
            ),
        )
        submitButton.setOnClickListener {
            if (answerET.text.toString().isEmpty()) {
                Toast.makeText(
                    requireContext(),
                    getString(R.string.enter_answer),
                    Toast.LENGTH_SHORT,
                ).show()
            } else {
                Toast.makeText(
                    requireContext(),
                    getString(R.string.security_que_added),
                    Toast.LENGTH_SHORT,
                ).show()
                requireActivity().onBackPressed()
            }
        }

        val accounts = listOf(
            Account(
                getString(R.string.sample_bank_2),
                getString(R.string.sample_bank_2_audit_control_number),
            ),
            Account(
                getString(R.string.sample_bank_home),
                getString(R.string.sample_home_audit_control_number),
            ),
            Account(
                getString(R.string.sample_bank_work),
                getString(R.string.sample_bank_audit_control_number),
            ),
        )
        accountDropDown.setAdapter(
            AccountTDAdapter(
                accounts,
            ),
        )
    }

    override fun onPause() {
        super.onPause()
        textDropdown.dismiss()
        accountDropDown.dismiss()
    }
}

data class Account(val name: String, val accountNumber: String)
