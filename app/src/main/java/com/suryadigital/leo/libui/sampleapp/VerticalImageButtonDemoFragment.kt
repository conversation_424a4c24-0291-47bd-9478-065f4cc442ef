package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton

/**
 * Demo fragment for Vertical Image Button.
 */
class VerticalImageButtonDemoFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_vertical_button_demo, container, false)
        setButtonClickListeners(rootView)
        return rootView
    }

    private fun setButtonClickListeners(rootView: View) {
        rootView.findViewById<VerticalImageButton>(R.id.warning_button)
            .setOnClickListener { showToast(getString(R.string.warning_button_clicked)) }
        rootView.findViewById<VerticalImageButton>(R.id.navigate_button)
            .setOnClickListener { showToast(getString(R.string.negative_button_clicked)) }
        rootView.findViewById<VerticalImageButton>(R.id.upload_button)
            .setOnClickListener { showToast(getString(R.string.upload_button_clicked)) }
        rootView.findViewById<VerticalImageButton>(R.id.finding_button)
            .setOnClickListener { showToast(getString(R.string.finding_button_clicked)) }
        rootView.findViewById<VerticalImageButton>(R.id.mark_important_button)
            .setOnClickListener { showToast(getString(R.string.mark_imp_button_clicked)) }
        rootView.findViewById<VerticalImageButton>(R.id.on_star_button)
            .setOnClickListener { showToast(getString(R.string.on_star_button_clicked)) }
        rootView.findViewById<VerticalImageButton>(R.id.mute_button)
            .setOnClickListener { showToast(getString(R.string.mute_button_clicked)) }
        rootView.findViewById<VerticalImageButton>(R.id.close_button)
            .setOnClickListener { showToast(getString(R.string.close_button_clicked)) }
    }

    private fun showToast(text: String) {
        Toast.makeText(activity, text, Toast.LENGTH_SHORT).show()
    }
}
