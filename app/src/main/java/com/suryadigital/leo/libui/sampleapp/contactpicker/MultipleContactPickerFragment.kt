package com.suryadigital.leo.libui.sampleapp.contactpicker

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.ActionBar
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.suryadigital.leo.libui.contactpicker.ContactPicker
import com.suryadigital.leo.libui.contactpicker.ContactPickerSelectionMode
import com.suryadigital.leo.libui.contactpicker.ContactPickerViewModel
import com.suryadigital.leo.libui.contactpicker.checkContactPermission
import com.suryadigital.leo.libui.sampleapp.HomeActivity
import com.suryadigital.leo.libui.sampleapp.R

class MultipleContactPickerFragment :
    Fragment(R.layout.fragment_layout_contact_picker),
    ContactPicker.PermissionRequestListener {
    private val contactPickerViewModel: ContactPickerViewModel by activityViewModels()
    private var actionBar: ActionBar? = null

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)

        actionBar = (requireActivity() as AppCompatActivity).supportActionBar
        val contactPicker = ContactPicker()
        contactPicker.setPermissionListener(this)
        contactPicker.setArguments(
            contactPickerViewModel = contactPickerViewModel,
            onCompletion = ::onContactSelectionCompleted,
            contactPickerSelectionMode = ContactPickerSelectionMode.MULTIPLE_SELECTION_CHOICE_MODE,
        )
        requireActivity()
            .supportFragmentManager
            .beginTransaction()
            .replace(R.id.contactFragmentContainer, contactPicker)
            .commit()
        updateToolbar()
    }

    private fun updateToolbar() {
        contactPickerViewModel.getSelectedContactCountLiveData().observe(
            viewLifecycleOwner,
        ) { count ->
            if (count > 0) {
                updateToolbarState(count)
            } else {
                resetToolbarState()
            }
        }
    }

    private fun updateToolbarState(count: Int) {
        actionBar?.apply {
            setHomeButtonEnabled(true)
            setDisplayHomeAsUpEnabled(true)
            title =
                requireContext().resources.getQuantityString(
                    R.plurals.selected_contact_count,
                    count,
                    count,
                )
        }
    }

    private fun resetToolbarState() {
        actionBar?.apply {
            setHomeButtonEnabled(false)
            setDisplayHomeAsUpEnabled(false)
            title = getString(R.string.home_toolbar_title)
        }
    }

    private fun onContactSelectionCompleted() {
        requireActivity().onBackPressedDispatcher.onBackPressed()
    }

    override fun requestPermission() {
        val parent = (requireActivity() as HomeActivity)
        if (!checkContactPermission(parent)) {
            parent.requestContactPermission()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        resetToolbarState()
        contactPickerViewModel.updateSelectedContactCountLiveData(0)
    }
}
