package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.hbb20.CountryCodePicker

/**
 * A simple [Fragment] subclass.
 */
class CountryPickerFragment : Fragment() {

    private lateinit var countryCodePicker: CountryCodePicker

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_country_picker, container, false)
        countryCodePicker = view.findViewById(R.id.countryCodeHolder)
        countryCodePicker.registerCarrierNumberEditText(view.findViewById(R.id.editText))
        countryCodePicker.setPhoneNumberValidityChangeListener {
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.number_is_valid), Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), getString(R.string.number_is_invalid), Toast.LENGTH_SHORT)
                    .show()
            }
        }
        return view
    }
}
