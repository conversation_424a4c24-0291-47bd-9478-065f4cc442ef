package com.suryadigital.leo.libui.sampleapp.listcomponent.model

import androidx.annotation.DrawableRes

sealed class DataModel

data class CategoryModel(
    val categoryName: String,
) : DataModel()

data class DataItem(
    val name: String,
    val phone: String = "",
    val lastPaidAmount: String = "",
    val lastPaidDate: String = "",
) : DataModel()

data class LoginModel(
    @DrawableRes
    val image: Int = 0,
    val userName: String,
) : DataModel()

data class MoreOptionModel(
    val title: String,
    val options: List<String>,
) : DataModel()
