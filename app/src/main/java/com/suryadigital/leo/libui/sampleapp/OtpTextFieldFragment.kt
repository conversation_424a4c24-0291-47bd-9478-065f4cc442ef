package com.suryadigital.leo.libui.sampleapp

import android.app.PendingIntent
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.otptextfield.AutoOTPReadListener
import com.suryadigital.leo.libui.otptextfield.OTPConfiguration
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import com.suryadigital.leo.libui.otptextfield.OTPTextField
import com.suryadigital.leo.libui.otptextfield.OTPTextFieldListener
import com.suryadigital.leo.libui.otptextfield.OtpReadType
import com.suryadigital.leo.libui.otptextfield.parseFirstNdigitOTPNumberInMessage

class OtpTextFieldFragment : Fragment(R.layout.fragment_otp_text_field) {
    private lateinit var firstOTPView: OTPTextField
    private lateinit var secondOTPView: OTPTextField
    private lateinit var thirdOTPView: OTPTextField
    private lateinit var firstResendButton: Button
    private lateinit var secondResendButton: Button
    private lateinit var thirdResendButton: Button

    companion object {
        private const val CORRECT_OTP = "123456"
        private val TAG = OtpTextFieldFragment::class.qualifiedName
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)

        firstOTPView = view.findViewById(R.id.otp_view)
        secondOTPView = view.findViewById(R.id.otp_view_second)
        thirdOTPView = view.findViewById(R.id.otp_view_third)
        firstResendButton = view.findViewById(R.id.resend_btn)
        secondResendButton = view.findViewById(R.id.resend_second_btn)
        thirdResendButton = view.findViewById(R.id.resend_third_btn)

        firstResendButton.setOnClickListener {
            firstOTPView.otp = "" // clear the input
        }
        secondResendButton.setOnClickListener {
            secondOTPView.otp = ""
        }
        thirdResendButton.setOnClickListener {
            thirdOTPView.otp = ""
        }
        firstOTPView.otpListener =
            object : OTPTextFieldListener {
                override fun onOTPComplete(otp: String) {
                    Toast.makeText(requireContext(), otp, Toast.LENGTH_SHORT).show()
                }

                override fun onInteractionListener() {
                }
            }
        secondOTPView.otpListener =
            object : OTPTextFieldListener {
                override fun onOTPComplete(otp: String) {
                    Toast.makeText(requireContext(), otp, Toast.LENGTH_SHORT).show()
                }

                override fun onInteractionListener() {
                }
            }

        thirdOTPView.otpListener =
            object : OTPTextFieldListener {
                override fun onOTPComplete(otp: String?) {
                    if (isAdded) {
                        if (otp == CORRECT_OTP) {
                            Toast
                                .makeText(
                                    requireContext(),
                                    getString(R.string.verified_successfully),
                                    Toast.LENGTH_SHORT,
                                ).show()
                            thirdOTPView.showSuccess()
                        } else {
                            thirdOTPView.showError()
                            Toast
                                .makeText(
                                    requireContext(),
                                    getString(R.string.incorrect_otp),
                                    Toast.LENGTH_SHORT,
                                ).show()
                        }
                    }
                }

                override fun onInteractionListener() {
                }
            }

        // To configure auto read without user consent
        val otpConfigSecond =
            OTPConfiguration(
                otpReadType = OtpReadType.AUTO_READ_AND_MANUAL_ENTRY,
                otpLength = 6,
            )
        with(thirdOTPView) {
            setConfiguration(otpConfigSecond)
            setAutoReadOtpListener(
                autoOTPReadListener =
                    object : AutoOTPReadListener {
                        override fun onSuccess(message: String) {
                            if (isAdded) {
                                Log.d(TAG, message) // parse the message and use.
                                val otpValue = parseFirstNdigitOTPNumberInMessage(message, otpConfigSecond.otpLength)
                                if (!otpValue.isNullOrEmpty()) {
                                    otp = otpValue
                                } else {
                                    Toast
                                        .makeText(
                                            requireContext(),
                                            context.getString(R.string.otp_not_found),
                                            Toast.LENGTH_SHORT,
                                        ).show()
                                }
                                Log.d(TAG, context.getString(R.string.auto_read_successful))
                            }
                        }

                        override fun onFailure(e: OTPFailureException) {
                            if (isAdded) {
                                Toast
                                    .makeText(
                                        requireContext(),
                                        context.getString(R.string.auto_read_failed),
                                        Toast.LENGTH_SHORT,
                                    ).show()
                            }
                        }

                        override fun onConsentIntentGenerated(
                            intent: Intent,
                            requestCode: Int,
                        ) {
                        }

                        override fun onMobileHintPickerIntentGenerated(
                            intent: PendingIntent,
                            requestCode: Int,
                        ) {
                            Log.d(TAG, context.getString(R.string.mobile_hint_generated_text))
                        }

                        override fun onAutoReadOTPFailed(e: OTPFailureException) {
                            Log.e(
                                TAG,
                                "SmsRetrieverClient failed to complete sms user consent or sms retriever task",
                                e,
                            )
                        }
                    },
            )
            startOtpReading()
        }
    }
}
