package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.landingscreen.LandingScreen
import com.suryadigital.leo.libui.landingscreen.LandingScreen.FinishButtonListener
import com.suryadigital.leo.libui.landingscreen.LandingScreenViewsConfigurer
import com.suryadigital.leo.libui.R as LibUIResource

class LandingScreenDemoFragment :
    Fragment(R.layout.landing_screen_demo_fragment),
    FinishButtonListener,
    LandingScreenViewsConfigurer {

    private lateinit var landingScreen: LandingScreen
    private val imagesList = listOf(
        R.mipmap.first_image,
        R.mipmap.second_image,
        R.mipmap.first_image,
        R.mipmap.second_image,
    )
    private lateinit var landingScreenTitle: List<String>
    private lateinit var landingScreenSubtitle: List<String?>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        landingScreen = view.findViewById(R.id.landingScreen)
        landingScreenTitle = listOf(
            getString(LibUIResource.string.secure),
            getString(LibUIResource.string.faster),
            getString(LibUIResource.string.earn_coins),
            getString(LibUIResource.string.efficient),
        )
        landingScreenSubtitle = listOf(
            getString(LibUIResource.string.secure_description),
            getString(LibUIResource.string.faster_description),
            getString(LibUIResource.string.earn_coins_description),
            null,
        )
        landingScreen.launch(landingScreenTitle.size, this, this)
    }

    override fun onFinishButtonClicked() {
        parentFragmentManager.popBackStack()
    }

    override fun configureLandingScreenViews(
        position: Int,
        imageView: ImageView,
        titleTV: TextView,
        subtitleTV: TextView,
    ) {
        imageView.setImageResource(imagesList[position])
        handleText(titleTV, subtitleTV, position)
    }

    private fun handleText(titleTV: TextView, subtitleTV: TextView, position: Int) {
        titleTV.text = landingScreenTitle[position]
        if (landingScreenSubtitle[position] != null) {
            subtitleTV.visibility = View.VISIBLE
            subtitleTV.text = landingScreenSubtitle[position]
        } else {
            subtitleTV.visibility = View.GONE
        }
    }
}
