package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.qrcode.BarcodeException
import com.suryadigital.leo.libui.qrcode.BarcodeFragment
import com.suryadigital.leo.libui.qrcode.BarcodeListener
import com.suryadigital.leo.libui.qrcode.ScanFormat
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted
import com.suryadigital.leo.libui.qrcode.setScanFormat

class BarcodeDemoFragment : Fragment(R.layout.barcode_demo_fragment) {

    private lateinit var scanTextView: TextView
    private lateinit var flashLightToggleButton: Button
    private lateinit var barcodeFragment: BarcodeFragment
    private var isFlashOn = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        scanTextView = view.findViewById(R.id.scan_tv)
        flashLightToggleButton = view.findViewById(R.id.flashlight_btn)
        barcodeFragment = BarcodeFragment()
        barcodeFragment.setScanFormat(ScanFormat.QR_CODE)
        barcodeFragment.setBarcodeListener(object : BarcodeListener {
            override fun onBarcodeValueReceived(rawValue: String) {
                scanTextView.text = rawValue
            }

            override fun onFailure(e: BarcodeException) {
                Log.d(">>>", e.message!!)
            }
        })
        addBarcodeFragment()
        flashLightToggleButton.setOnClickListener {
            if (checkCameraPermissionGranted(requireContext())) {
                isFlashOn = !isFlashOn
                barcodeFragment.toggleFlashLight(isFlashOn)
                if (isFlashOn) {
                    flashLightToggleButton.text = getString(R.string.label_flashlight_off)
                } else {
                    flashLightToggleButton.text = getString(R.string.label_flashlight_on)
                }
            }
        }
    }

    private fun addBarcodeFragment() {
        childFragmentManager.beginTransaction()
            .add(R.id.barcode_fragment_container, barcodeFragment)
            .commit()
    }

    override fun onResume() {
        super.onResume()
        if (!checkCameraPermissionGranted(requireContext())) {
            scanTextView.text = requireContext().getString(R.string.camera_permission_denied)
        } else {
            scanTextView.text = ""
        }
        barcodeFragment.startBarcodeDetection()
    }

    override fun onPause() {
        super.onPause()
        if (isFlashOn) {
            isFlashOn = false
            barcodeFragment.toggleFlashLight(false)
            flashLightToggleButton.text = getString(R.string.label_flashlight_on)
        }
    }
}
