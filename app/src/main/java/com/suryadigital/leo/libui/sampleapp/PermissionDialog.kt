package com.suryadigital.leo.libui.sampleapp

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import android.widget.LinearLayout
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.DialogFragment

class PermissionDialog : DialogFragment() {
    interface PermissionDialogListener {
        fun onProceed()

        fun onDecline()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.permission_dialog, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(rootView)
        isCancelable = false
        return rootView
    }

    private fun initViews(rootView: View) {
        rootView.findViewById<Button>(R.id.proceed).setOnClickListener {
            (activity as PermissionDialogListener).onProceed()
            dismiss()
        }

        rootView.findViewById<Button>(R.id.decline).setOnClickListener {
            (activity as PermissionDialogListener).onDecline()
        }
    }

    override fun onStart() {
        super.onStart()
        /*
            Maintaining the width of this dialog to 85% of device's width.
         */
        val calculatedWidth = (requireContext().resources.displayMetrics.widthPixels * 85) / 100
        val dialogWidth = if (calculatedWidth > FIXED_DIALOG_WIDTH) FIXED_DIALOG_WIDTH else calculatedWidth
        val dialogHeight = LinearLayout.LayoutParams.WRAP_CONTENT

        dialog?.window?.setLayout(dialogWidth, dialogHeight)
    }

    companion object {
        const val TAG: String = "libui.PermissionDialog"
    }
}

private const val FIXED_DIALOG_WIDTH = 1224
