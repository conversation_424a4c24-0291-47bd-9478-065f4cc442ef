package com.suryadigital.leo.libui.sampleapp

import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.suryadigital.leo.libui.passwordfield.PasswordFieldException
import com.suryadigital.leo.libui.passwordfield.PasswordInputLayout
import com.suryadigital.leo.libui.passwordfield.PasswordTextField

class PasswordFieldDemo : Fragment(R.layout.fragment_password_field_demo) {
    private lateinit var firstPasswordPIL: PasswordInputLayout
    private lateinit var firstPasswordET: PasswordTextField
    private lateinit var secondPasswordPIL: PasswordInputLayout
    private lateinit var secondPasswordET: PasswordTextField
    private lateinit var thirdPasswordPIL: PasswordInputLayout
    private lateinit var firstResendButton: Button
    private lateinit var secondResendButton: Button

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)

        firstPasswordPIL = view.findViewById(R.id.passwordLayout)
        firstPasswordET = view.findViewById(R.id.passwordEditText)
        secondPasswordPIL = view.findViewById(R.id.passwordLayoutSecond)
        secondPasswordET = view.findViewById(R.id.passwordEditTextSecond)
        thirdPasswordPIL = view.findViewById(R.id.passwordLayoutThird)
        firstResendButton = view.findViewById(R.id.toastPasswordBtn)
        secondResendButton = view.findViewById(R.id.toastPasswordBtnSecond)

        with(firstPasswordPIL) {
            setPasswordIconTint(Color.BLACK)
        }
        with(firstPasswordET) {
            setMinimumPasswordLength(5)
        }
        firstResendButton.setOnClickListener {
            try {
                Toast
                    .makeText(
                        requireContext(),
                        firstPasswordET.enteredPassword(),
                        Toast.LENGTH_SHORT,
                    ).show()
            } catch (e: PasswordFieldException) {
                Toast.makeText(requireContext(), e.localizedMessage, Toast.LENGTH_SHORT).show()
            }
        }

        with(secondPasswordPIL) {
            displayShowHidePasswordButton(false)
        }

        secondResendButton.setOnClickListener {
            if (secondPasswordET.enteredPassword().isNotEmpty()) {
                Toast
                    .makeText(
                        requireContext(),
                        secondPasswordET.enteredPassword(),
                        Toast.LENGTH_SHORT,
                    ).show()
            }
        }

        thirdPasswordPIL.setPasswordIconTint(Color.RED)
    }
}
