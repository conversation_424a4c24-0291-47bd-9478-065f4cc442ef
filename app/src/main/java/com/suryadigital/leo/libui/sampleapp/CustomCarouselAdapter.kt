package com.suryadigital.leo.libui.sampleapp

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.suryadigital.leo.libui.carouselview.CarouselAdapter

class ViewHolder(
    view: View,
) : RecyclerView.ViewHolder(view) {
    private val textView: TextView = view.findViewById(R.id.textview)

    fun bind(string: String) {
        textView.text = string
    }
}

class CustomCarouselAdapter(
    private val context: Context,
    private val views: List<String>,
) : CarouselAdapter<String, ViewHolder>(views) {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder = ViewHolder(LayoutInflater.from(context).inflate(R.layout.carousel_recycler_item, parent, false))

    override fun onBindView(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.bind(views[position])
    }
}
