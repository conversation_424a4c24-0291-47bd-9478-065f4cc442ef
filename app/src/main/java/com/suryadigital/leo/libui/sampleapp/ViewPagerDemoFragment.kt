package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.view.View
import android.webkit.WebView
import android.widget.Button
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.suryadigital.leo.libui.viewpager.ViewPager
import com.suryadigital.leo.libui.viewpager.ViewPagerItemClickListener

class ViewPagerDemoFragment : Fragment(R.layout.view_pager_demo_fragment) {

    private lateinit var viewPager: ViewPager
    private val viewPagerViewModel by activityViewModels<ViewPagerViewModel>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        viewPager = view.findViewById(R.id.viewPager)
        val imageWithText = View.inflate(requireContext(), R.layout.layout_image_with_text, null)
        val imageWithButton =
            View.inflate(requireContext(), R.layout.layout_image_with_button, null)
        imageWithButton.findViewById<Button>(R.id.button).setOnClickListener {
            Toast.makeText(requireContext(), getString(R.string.clicked), Toast.LENGTH_SHORT).show()
        }

        // Note : web touch cannot be overriden, so touch won't work on webview item
        val webViewLayout = View.inflate(requireContext(), R.layout.layout_webview, null)
        val webView: WebView = webViewLayout.findViewById(R.id.webView)
        webView.loadUrl("https://www.surya-soft.com")

        val multiViewList = listOf<View>(imageWithText, imageWithButton, webViewLayout)
        viewPager.setViews(multiViewList)
        viewPager.setPageClickListener(object : ViewPagerItemClickListener {
            override fun onItemClickListener(index: Int) {
                Toast.makeText(requireContext(), getString(R.string.clicked_arg, index), Toast.LENGTH_SHORT).show()
            }
        })
        viewPager.playViewPager()

        // set retain state of viewpager
        viewPager.setCurrentIndex(viewPagerViewModel.pageIndexLiveData.value!!)
    }

    override fun onStop() {
        super.onStop()
        // To handle the configuration change
        viewPager.stopViewPager()
        viewPagerViewModel.pageIndexLiveData.value = viewPager.getCurrentIndex()
    }
}
