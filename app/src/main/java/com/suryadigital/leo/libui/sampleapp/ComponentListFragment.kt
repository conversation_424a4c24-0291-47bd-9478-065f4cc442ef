package com.suryadigital.leo.libui.sampleapp

import android.os.Bundle
import android.view.View
import android.widget.ArrayAdapter
import android.widget.ListView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.ViewModelProvider
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted

class ComponentListFragment : Fragment(R.layout.component_list_fragment) {

    private lateinit var listView: ListView
    private val viewModelFactory by lazy { ViewModelFactory(requireContext()) }
    private val componentListViewModel by lazy { ViewModelProvider(this, viewModelFactory).get(ComponentListFragmentViewModel::class.java) }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val components = mutableListOf<String>().apply {
            componentListViewModel.components.forEach {
                add(it.first)
            }
        }
        val adapter = ArrayAdapter(
            view.context,
            R.layout.component_list_item,
            R.id.component_list_name,
            components,
        )
        listView = view.findViewById(R.id.listView)
        listView.adapter = adapter
        listView.setOnItemClickListener { _, _, pos, _ ->
            val fragment = componentListViewModel.components[pos].second()
            val parent = (requireActivity() as HomeActivity)
            if (fragment is BarcodeDemoFragment && !checkCameraPermissionGranted(parent)) {
                parent.requestCameraPermission()
            } else if (fragment is ImagePickerFragment && !checkCameraPermissionGranted(parent)) {
                parent.requestCameraPermission()
            } else {
                activity?.apply {
                    supportFragmentManager
                        .beginTransaction()
                        .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
                        .replace(
                            R.id.nav_host_fragment,
                            fragment,
                        )
                        .addToBackStack("homepage")
                        .commit()
                }
            }
        }
    }
}
