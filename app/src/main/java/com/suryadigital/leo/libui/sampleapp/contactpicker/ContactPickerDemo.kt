package com.suryadigital.leo.libui.sampleapp.contactpicker

import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.ListView
import android.widget.TextView
import androidx.core.net.toUri
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.suryadigital.leo.libui.contactpicker.Contact
import com.suryadigital.leo.libui.contactpicker.ContactPickerViewModel
import com.suryadigital.leo.libui.sampleapp.R

class ContactPickerDemo : Fragment(R.layout.demo_contact_picker) {

    private val contactPickerViewModel: ContactPickerViewModel by activityViewModels()

    private lateinit var demoNameListView: ListView
    private lateinit var selectContactButton: Button
    private lateinit var selectMultiContactButton: Button
    private lateinit var contactIV: ImageView
    private lateinit var contactNameTV: TextView
    private lateinit var mobileNumberTV: TextView
    private lateinit var selectedContactNumber: View

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        demoNameListView = view.findViewById(R.id.demo_name_list_view)
        selectContactButton = view.findViewById(R.id.selectContactBtn)
        selectMultiContactButton = view.findViewById(R.id.select_mul_contact_btn)
        contactIV = view.findViewById(R.id.contact_image)
        contactNameTV = view.findViewById(R.id.contact_name)
        mobileNumberTV = view.findViewById(R.id.mobile_number)
        selectedContactNumber = view.findViewById(R.id.selectedContactNumber)

        contactPickerViewModel.getContactNumberLiveData().observe(
            viewLifecycleOwner,
        ) { contactDetails ->
            if (isAdded) {
                val imageUri = contactDetails.imageUri
                if (contactDetails.name.isNotBlank()) {
                    selectedContactNumber.visibility = View.VISIBLE
                    contactNameTV.text = contactDetails.name
                    if (imageUri != null) {
                        contactIV.setImageURI(imageUri.toUri())
                    }
                    mobileNumberTV.text = contactDetails.contactNumber
                } else {
                    selectedContactNumber.visibility = View.GONE
                }
            }
        }

        selectContactButton.setOnClickListener {
            val singleContactPickerFragment = SingleContactPickerFragment()
            activity?.apply {
                supportFragmentManager
                    .beginTransaction()
                    .replace(R.id.nav_host_fragment, singleContactPickerFragment)
                    .addToBackStack("homepage")
                    .commit()
            }
        }

        initAdapter().apply {
            contactPickerViewModel.getSelectedContactsLiveData().observe(
                viewLifecycleOwner,
            ) {
                second.addAll(it)
                if (isAdded) {
                    first.notifyDataSetChanged()
                }
            }
        }

        selectMultiContactButton.setOnClickListener {
            val multipleContactPicker = MultipleContactPickerFragment()
            activity?.apply {
                supportFragmentManager
                    .beginTransaction()
                    .replace(R.id.nav_host_fragment, multipleContactPicker)
                    .addToBackStack("homepage")
                    .commit()
            }
        }
    }

    private fun initAdapter(): Pair<PreviewContactListAdapter, ArrayList<Contact>> {
        val contacts = ArrayList<Contact>()
        val adapter = PreviewContactListAdapter(
            requireContext(),
            contacts,
        )
        demoNameListView.adapter = adapter
        return adapter to contacts
    }
}
