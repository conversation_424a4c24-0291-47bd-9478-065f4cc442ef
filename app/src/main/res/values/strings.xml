<resources>
    <string name="app_name" translatable="false">AndroidUIComponents</string>
    <string name="mobile_input">Enter mobile number</string>
    <string name="demo_text">Image is taken from pixabay.com</string>
    <string name="image">Image</string>
    <string name="second_image">Second image</string>
    <string name="button">Button</string>
    <string name="resend">Resend</string>
    <string name="camera_permission_denied">Camera permission is denied, turn on from settings or restart and accept permission to use this service.</string>
    <string name="tv_date_format">Date : %s</string>
    <string name="choose_date">Select date</string>
    <string name="red_theme">Red theme</string>
    <string name="green_theme">Green theme</string>
    <string name="verified_successfully">Verified successfully</string>
    <string name="incorrect_otp">Incorrect OTP!</string>
    <string name="otp_not_found">OTP not found!</string>
    <string name="auto_read_failed">Auto Read Failed!</string>
    <string name="auto_read_successful">Auto read successful!</string>
    <string name="mobile_hint_generated_text">Mobile Hint intent generated, start the mobile hint picker activity</string>
    <string name="consent_denied">Consent denied</string>
    <string name="enter_password">Enter password</string>
    <string name="toast_password">Toast password</string>
    <string name="without_eye">Password field without eye icon</string>
    <string name="with_icon">Password field with eye icon</string>
    <string name="possible_design">Other possible designs</string>
    <string name="download">Download</string>
    <string name="login_successfully">Login successful</string>
    <string name="sigin_successfully">Sign in successful</string>
    <string name="download_action">Download action completed</string>
    <string name="subscribed">Subscribed to kedwig repository</string>
    <string name="submit_action">Submit action completed</string>
    <string name="ok_tap">OK Done</string>
    <string name="question1">Which is your favourite sport?</string>
    <string name="question2">What was the name of your first pet?</string>
    <string name="question3">What is the first thing you learned to cook?</string>
    <string name="question4">What is the first name of your childhood friend?</string>
    <string name="question5">What was the first film you saw in theater?</string>
    <string name="select_your_security_question">Select your security question:</string>
    <string name="answer">Answer</string>
    <string name="submit">Submit</string>
    <string name="enter_answer">Please enter your answer!</string>
    <string name="select_your_account">Select your account:</string>
    <string name="sample_bank_2" translatable="false">ICICI</string>
    <string name="sample_bank_2_audit_control_number" translatable="false">***********</string>
    <string name="sample_bank_work">FDH-Work</string>
    <string name="sample_bank_audit_control_number" translatable="false">***********</string>
    <string name="sample_bank_home">FDH-Home</string>
    <string name="sample_home_audit_control_number" translatable="false">***********</string>
    <string name="dropdown_button_icon">Dropdown button icon</string>
    <string name="security_que_added">Security question added successfully</string>
    <string name="selected_contact">Select contact</string>
    <string name="select_multiple_contact">Select multiple contact</string>
    <string name="camera_permission_granted">Camera permission granted</string>
    <string name="home_toolbar_title" translatable="false">Android Components</string>
    <plurals name="selected_contact_count">
        <item quantity="one">%d selected</item>
        <item quantity="other">%d selected</item>
    </plurals>
    <string name="image_load_error">Error occurred while loading an image.</string>
    <string name="marker_selected">Marker Selected: %s</string>
    <string name="hospital">Hospital</string>
    <string name="local_bar">Local Bar</string>
    <string name="temple">Temple</string>
    <string name="restaurant">Restaurant</string>
    <string name="lake">Lake</string>
    <string name="surya_software" translatable="false">Surya Software</string>
    <string name="english" translatable="false">English</string>
    <string name="nyanja" translatable="false">Nyanja</string>

    <string name="country_picker_fragment">Country Picker Fragment</string>
    <string name="vertical_image_button_fragment">Vertical Image Button Fragment</string>
    <string name="view_pager_fragment">View Pager Fragment</string>
    <string name="carousel_view">Carousel View</string>
    <string name="otp_text_field">OTP Text Field</string>
    <string name="otp_with_consent">OTP With Consent</string>
    <string name="barcode">Barcode</string>
    <string name="date_picker">Date Picker</string>
    <string name="password_text_field">Password Text Field</string>
    <string name="progress_button">Progress Button</string>
    <string name="text_dropdown">Text Dropdown</string>
    <string name="contact_picker">Contact Picker</string>
    <string name="location">Location</string>
    <string name="list_component">List Component</string>

    <string name="login">Login</string>
    <string name="sign_in">Sign In</string>
    <string name="subscribe">Subscribe</string>
    <string name="ok">OK</string>

    <string name="number_is_valid">Number is valid</string>
    <string name="number_is_invalid">Number is invalid</string>

    <string name="warning_button_clicked">Warning button clicked</string>
    <string name="negative_button_clicked">Navigate button clicked</string>
    <string name="upload_button_clicked">Upload button clicked</string>
    <string name="finding_button_clicked">Finding button clicked</string>
    <string name="mark_imp_button_clicked">Mark important button clicked</string>
    <string name="on_star_button_clicked">On Star button clicked</string>
    <string name="mute_button_clicked">Mute button clicked</string>
    <string name="close_button_clicked">Close button clicked</string>

    <string name="clicked">Clicked</string>
    <string name="clicked_arg">Clicked %d</string>

    <string name="red_theme_applied">Red theme applied successfully</string>
    <string name="green_theme_applied">Green theme applied successfully</string>

    <string name="navigate_to_dest">NAVIGATE TO DEST</string>
    <string name="upload">Upload</string>
    <string name="finding">Finding</string>
    <string name="mark_important">Mark Important</string>
    <string name="star_on">Star On</string>
    <string name="mute">MUTE</string>
    <string name="select_the_icon_to_close">Select the icon to close</string>

    <string name="lastPaid">Last Paid: %1$s on %2$s</string>
    <string name="multi_textview_example">Multi Text View Example</string>
    <string name="button_example">Buttons Example</string>
    <string name="demo_user_1">Alice</string>
    <string name="demo_user_2">Bob</string>
    <string name="demo_contact_text" translatable="false">+91–99XXXXXXXX</string>
    <string name="demo_last_paid_amount_text" translatable="false">Rs.5000</string>
    <string name="demo_last_paid_date_text" translatable="false">29 JUN 2020</string>
    <string name="user_logged_in">%s Logged In</string>
    <string name="log_out">Log Out</string>
    <string name="user_logged_out">%s Logged Out</string>
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="more_option_example">More Options Example</string>
    <string name="click_for_more_options">Click For More Options</string>
    <string name="you_selected">You Selected: %s</string>
    <string name="profile_image">Profile Image</string>
    <string name="search">Search</string>
    <string name="translate">Change Language</string>
    <string name="john_smith">John Smith</string>
    <string name="tom_hank">Tom Hank</string>
    <string name="demo_mobile_number_one">+918877456321</string>
    <string name="jhonny_walker">Jhonny Walker</string>
    <string name="contact_view_demo">Contact View</string>
    <string name="landing_screen_demo">Landing Screen</string>
    <string name="decline">Decline</string>
    <string name="open_settings">Open Settings</string>
    <string name="permission_required">Permission Required</string>
    <string name="contact_permission_description">Please allow permission to your address book to search your contacts</string>
    <string name="image_picker">ImagePicker</string>
    <string name="pick_image">Pick Image</string>
    <string name="size_of_selected_image_d" translatable="false">Size of selected image = %d Kb</string>
    <string name="choose">Choose</string>
    <string name="take_a_picture">Take a Picture</string>
    <string name="pick_from_gallery">Pick from Gallery</string>
    <string name="keyboard_demo">Pin Keyboard</string>
    <string name="go_to">Go To</string>
    <string name="label_flashlight_on">Flashlight On</string>
    <string name="label_flashlight_off">Flashlight Off</string>
</resources>
