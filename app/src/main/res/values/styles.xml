<resources>

    <style name="AppTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="RedDatePickerDialogTheme" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="colorAccent">@color/red</item>
        <item name="colorControlNormal">@color/red</item>
    </style>

    <style name="GreenDatePickerDialogTheme" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="colorAccent">@color/green_300</item>
        <item name="colorControlNormal">@color/green_300</item>
    </style>

    <style name="MaterialTextInputLayoutTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

        <!--   This two attributes controls the background tint of password field in normal and activated state     -->
        <item name="colorControlActivated">@color/black</item>
        <item name="colorControlNormal">@color/upArrowBtnColor</item>
        <item name="actionMenuTextColor">@android:color/white</item>
    </style>

    <style name="CategoryTextViewTheme" parent="@android:style/Widget.TextView">
        <item name="android:background">@color/categoryBackground</item>
    </style>

    <style name="AlertDialogStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/bg_5dp</item>
    </style>

    <style name="AlertButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@android:color/transparent</item>
    </style>

</resources>
