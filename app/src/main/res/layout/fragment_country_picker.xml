<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:padding="10dp"
    tools:context=".CountryPickerFragment">

    <com.hbb20.CountryCodePicker
        android:id="@+id/countryCodeHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:ccp_contentColor="@color/textColor"
        app:ccp_arrowColor="@color/arrowColor"
        app:ccpDialog_textColor="@color/textColor"/>

    <EditText
        android:id="@+id/editText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="phone"
        android:hint="@string/mobile_input"
        android:autofillHints="phonenumber"
        android:backgroundTint="@color/colorAccent"/>

</LinearLayout>
