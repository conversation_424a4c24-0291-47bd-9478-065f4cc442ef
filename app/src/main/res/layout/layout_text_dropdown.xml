<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".textdropdowndemo.TextDropdownDemo">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/select_your_security_question"
        android:textColor="@color/blue_600" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/dropdown_background_color">

        <com.suryadigital.leo.libui.textdropdown.TextDropdown
            android:id="@+id/text_dropdown"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:dropDownWidth="300dp" />
    </FrameLayout>

    <EditText
        android:id="@+id/answer_et"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:autofillHints="@null"
        android:backgroundTint="@color/colorAccent"
        android:hint="@string/answer"
        android:inputType="text"
        android:maxLength="15"
        android:textCursorDrawable="@drawable/black_cursor_drawable"
        android:paddingStart="10dp"
        android:paddingEnd="10dp" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/submit_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/submit"
        android:backgroundTint="@color/colorPrimary"
        android:textColor="@color/white" />

    <TextView
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/select_your_account"
        android:textColor="@color/blue_600" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/dropdown_background_color">

        <com.suryadigital.leo.libui.textdropdown.TextDropdown
            android:id="@+id/account_text_dropdown"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:dropDownWidth="250dp" />
    </FrameLayout>
</LinearLayout>
