<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        tools:context=".ContactImageViewDemo">

        <com.suryadigital.leo.libui.contactview.ContactIconView
            android:id="@+id/contactImageView"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="10dp"
            app:cardCornerRadius="24dp" />

        <TextView
            android:id="@+id/name_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_toEndOf="@id/contactImageView"
            android:text="@string/john_smith" />

        <TextView
            android:id="@+id/mobile_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/name_tv"
            android:layout_toEndOf="@id/contactImageView"
            android:text="@string/demo_mobile_number_one" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        tools:context=".ContactImageViewDemo">

        <com.suryadigital.leo.libui.contactview.ContactIconView
            android:id="@+id/second_contactImageView"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="10dp"
            app:cardCornerRadius="24dp" />

        <TextView
            android:id="@+id/second_name_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_toEndOf="@id/second_contactImageView"
            android:text="@string/tom_hank" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/second_name_tv"
            android:layout_toEndOf="@id/second_contactImageView"
            android:text="@string/demo_mobile_number_one" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        tools:context=".ContactImageViewDemo">

        <com.suryadigital.leo.libui.contactview.ContactIconView
            android:id="@+id/third_contactImageView"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="10dp"
            app:cardCornerRadius="24dp" />

        <TextView
            android:id="@+id/third_name_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_toEndOf="@id/third_contactImageView"
            android:text="@string/jhonny_walker"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/third_name_tv"
            android:layout_toEndOf="@id/third_contactImageView"
            android:text="@string/demo_mobile_number_one" />

    </RelativeLayout>
</LinearLayout>
