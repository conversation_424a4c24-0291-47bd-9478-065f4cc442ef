<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/swipeToRefresh"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.suryadigital.leo.libui.carouselview.CarouselRecyclerView
                android:id="@+id/carouselView"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                app:indicatorVisible="true"
                app:carouselSelectedIndicatorColor="@android:color/holo_blue_light"
                app:unselectedIndicatorColor="@android:color/holo_orange_dark" />

            <com.suryadigital.leo.libui.carouselview.CarouselRecyclerView
                android:id="@+id/carouselViewMiddle"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="200dp"
                android:orientation="horizontal"
                app:carouselSelectedIndicatorColor="@android:color/holo_blue_light"
                app:unselectedIndicatorColor="@android:color/holo_orange_dark" />

        </LinearLayout>

    </ScrollView>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
