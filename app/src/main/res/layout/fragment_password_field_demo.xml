<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/MaterialTextInputLayoutTheme"
    tools:context=".PasswordFieldDemo">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:text="@string/with_icon"
        android:textColor="@color/blue_600"
        android:textStyle="bold" />

    <com.suryadigital.leo.libui.passwordfield.PasswordInputLayout
        android:id="@+id/passwordLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.suryadigital.leo.libui.passwordfield.PasswordTextField
            android:id="@+id/passwordEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/transparent"
            android:hint="@string/enter_password" />
    </com.suryadigital.leo.libui.passwordfield.PasswordInputLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/toastPasswordBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:backgroundTint="@color/black"
        android:text="@string/toast_password"
        android:textColor="@color/white" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:text="@string/without_eye"
        android:textColor="@color/blue_600"
        android:textStyle="bold" />

    <com.suryadigital.leo.libui.passwordfield.PasswordInputLayout
        android:id="@+id/passwordLayoutSecond"
        style="@style/Widget.Design.TextInputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.suryadigital.leo.libui.passwordfield.PasswordTextField
            android:id="@+id/passwordEditTextSecond"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:hint="@string/enter_password"
            android:textSize="14sp" />
    </com.suryadigital.leo.libui.passwordfield.PasswordInputLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/toastPasswordBtnSecond"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:backgroundTint="@color/red_600"
        android:text="@string/toast_password"
        android:textColor="@color/white" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="10dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:text="@string/possible_design"
        android:textColor="@color/blue_600"
        android:textStyle="bold" />

    <com.suryadigital.leo.libui.passwordfield.PasswordInputLayout
        android:id="@+id/passwordLayoutThird"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.suryadigital.leo.libui.passwordfield.PasswordTextField
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/black"
            android:hint="@string/enter_password" />
    </com.suryadigital.leo.libui.passwordfield.PasswordInputLayout>

    <com.suryadigital.leo.libui.passwordfield.PasswordInputLayout
        style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp">

        <com.suryadigital.leo.libui.passwordfield.PasswordTextField
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/enter_password" />
    </com.suryadigital.leo.libui.passwordfield.PasswordInputLayout>


</LinearLayout>
