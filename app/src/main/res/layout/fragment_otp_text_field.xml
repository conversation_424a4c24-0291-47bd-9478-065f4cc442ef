<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".OtpTextFieldFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@color/red_600"
        android:gravity="center"
        android:orientation="horizontal">

        <com.suryadigital.leo.libui.otptextfield.OTPTextField
            android:id="@+id/otp_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            app:bar_active_color="@color/active_otp_bar_color"
            app:bar_enabled="true"
            app:bar_height="2dp"
            app:height="40dp"
            app:length="6"
            app:otp_text_size="16sp"
            app:width="32dp" />

        <Button
            android:id="@+id/resend_btn"
            style="?borderlessButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/resend"
            android:textAllCaps="false"
            android:textColor="@color/active_otp_bar_color"
            android:textStyle="bold" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginTop="16dp"
        android:background="@color/colorPrimary"
        android:gravity="center"
        android:orientation="horizontal">

        <com.suryadigital.leo.libui.otptextfield.OTPTextField
            android:id="@+id/otp_view_second"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/black"
            app:bar_active_color="@color/black"
            app:bar_enabled="true"
            app:bar_height="2dp"
            app:height="32dp"
            app:hide_otp="true"
            app:hide_otp_drawable="@drawable/pin_active_color"
            app:length="4"
            app:otp_text_size="16sp"
            app:width="32dp" />

        <Button
            android:id="@+id/resend_second_btn"
            style="?borderlessButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/resend"
            android:textAllCaps="false"
            android:textColor="@color/black"
            android:textStyle="bold" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginTop="16dp"
        android:background="@color/colorPrimary"
        android:gravity="center"
        android:orientation="horizontal">

        <com.suryadigital.leo.libui.otptextfield.OTPTextField
            android:id="@+id/otp_view_third"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            app:height="40dp"
            app:length="6"
            app:otp_box_background_error="@drawable/bg_otp_box_error"
            app:otp_box_background_success="@drawable/bg_otp_box_success"
            app:otp_box_background_active="@drawable/bg_otp_box_active"
            app:otp_box_background="@drawable/bg_otp_box"
            app:otp_text_size="16sp"
            app:width="32dp" />

        <Button
            android:id="@+id/resend_third_btn"
            style="?borderlessButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/resend"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>
