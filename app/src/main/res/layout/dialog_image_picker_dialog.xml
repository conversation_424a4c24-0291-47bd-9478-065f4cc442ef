<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    style="@style/AlertDialogStyle"
    android:padding="@dimen/dimen_16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/choose"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/choose"
        android:textSize="22sp"
        android:textStyle="bold"
        android:padding="@dimen/dimen_8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/take_a_picture"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/take_a_picture"
        android:textSize="16sp"
        android:gravity="center"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/dimen_8dp"
        app:layout_constraintTop_toBottomOf="@+id/choose"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/select_from_gallery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/pick_from_gallery"
        android:textSize="16sp"
        android:gravity="center"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/dimen_8dp"
        app:layout_constraintTop_toBottomOf="@+id/take_a_picture"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
