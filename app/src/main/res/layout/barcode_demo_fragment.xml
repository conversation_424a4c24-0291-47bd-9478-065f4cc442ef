<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".BarcodeDemoFragment">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/barcode_fragment_container"
        android:layout_width="225dp"
        android:layout_height="300dp"
        android:tag="barcode_fragment"/>

    <TextView
        android:textColor="@color/black"
        android:id="@+id/scan_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textSize="16sp"
        android:layout_gravity="center_horizontal"/>

    <Button
        android:textColor="@color/black"
        android:id="@+id/flashlight_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textSize="16sp"
        android:text="@string/label_flashlight_on"
        android:layout_gravity="center_horizontal"/>

</LinearLayout>
