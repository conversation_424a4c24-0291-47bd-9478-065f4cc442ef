<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:orientation="vertical"
        android:padding="16dp">

        <include
            android:id="@+id/selectedContactNumber"
            layout="@layout/contact_demo_list_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <Button
            android:id="@+id/selectContactBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:backgroundTint="@color/colorPrimary"
            android:text="@string/selected_contact"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/select_mul_contact_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:backgroundTint="@color/colorAccent"
            android:text="@string/select_multiple_contact"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/selectContactBtn" />

        <ListView
            android:id="@+id/demo_name_list_view"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:nestedScrollingEnabled="true"
            tools:ignore="NestedScrolling" />

    </LinearLayout>

</ScrollView>
