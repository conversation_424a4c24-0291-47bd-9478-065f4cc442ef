<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:gravity="center"
        android:background="@color/colorPrimaryDark">

        <EditText
            android:id="@+id/etSearch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:inputType="text"
            android:textColor="@color/greyDark"
            android:gravity="center_vertical"
            android:background="@color/white"
            android:drawableEnd="@drawable/ic_search"
            android:hint="@string/search"
            android:importantForAutofill="no" />

    </LinearLayout>

    <com.suryadigital.leo.libui.listview.ListRecyclerView
        android:id="@+id/listRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
