<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="ProgressButtonDemo">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.suryadigital.leo.libui.progressbutton.ProgressButton
            android:id="@+id/login_button"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            app:buttonBackgroundColor="@color/colorAccent"
            app:buttonText="@string/login"
            app:buttonTextColor="@color/white" />

        <com.suryadigital.leo.libui.progressbutton.ProgressButton
            android:id="@+id/sigin_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:buttonBackgroundColor="@color/colorPrimary"
            app:buttonText="@string/sign_in"
            app:buttonTextColor="@color/white" />

        <com.suryadigital.leo.libui.progressbutton.ProgressButton
            android:id="@+id/download_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:buttonBackgroundColor="@color/defaultContentColor"
            app:buttonRadius="20dp"
            app:buttonTextColor="@color/white" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <com.suryadigital.leo.libui.progressbutton.ProgressButton
                android:id="@+id/subscribe_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:buttonBackgroundColor="@android:color/holo_blue_dark"
                app:buttonRadius="6dp"
                app:buttonTextFontStyle="bold"
                app:buttonText="@string/subscribe"
                app:buttonTextColor="@color/white" />

            <com.suryadigital.leo.libui.progressbutton.ProgressButton
                android:id="@+id/submit_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:buttonBackgroundColor="@android:color/holo_red_dark"
                app:buttonRadius="6dp"
                app:buttonText="@string/submit"
                app:buttonTextFontStyle="italic"
                app:buttonTextColor="@color/black" />

        </LinearLayout>

        <com.suryadigital.leo.libui.progressbutton.ProgressButton
            android:id="@+id/ok_button"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            app:buttonBackgroundColor="@android:color/holo_red_dark"
            app:buttonRadius="50dp"
            app:buttonText="@string/ok"
            app:buttonTextFontStyle="bold"
            app:buttonTextColor="@color/white" />
    </LinearLayout>
</ScrollView>
