<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ImagePickerFragment">

    <Button
        android:id="@+id/pick_image_bt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pick_image"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_centerInParent="true"
        app:layout_constraintTop_toBottomOf="@+id/image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:layout_marginTop="@dimen/dimen_16dp"
        android:textSize="18sp"
        android:visibility="gone"
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/size_of_selected_image_d"
        android:id="@+id/size_tv"
        android:layout_marginBottom="@dimen/dimen_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/image"/>

    <ImageView
        android:layout_marginBottom="24dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="200dp"
        android:minHeight="200dp"
        android:layout_centerHorizontal="true"
        android:layout_above="@id/pick_image_bt"
        android:background="@color/black"
        android:scaleType="centerCrop"
        android:id="@+id/image"
        tools:ignore="ContentDescription"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
