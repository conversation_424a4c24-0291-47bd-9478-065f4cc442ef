<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical">


    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/map"
        android:name="com.suryadigital.leo.libui.location.MapFragment"
        android:layout_width="match_parent"
        android:layout_height="650dp" />

    <Button
        android:id="@+id/go_to_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/go_to"/>


</LinearLayout>
