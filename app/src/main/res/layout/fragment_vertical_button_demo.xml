<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="10dp"
    tools:context=".CountryPickerFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/warning_button"
            android:layout_width="150dp"
            android:layout_height="150dp"
            app:iconBackgroundColor="@android:color/holo_orange_dark"
            app:iconBackgroundCornerRadius="75dp"
            app:iconHeightPercent="0.99"
            app:rippleRadius="170"
            app:iconSource="@android:drawable/ic_dialog_alert" />

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/navigate_button"
            android:layout_width="100dp"
            android:layout_height="130dp"
            android:layout_marginStart="20dp"
            app:iconBackgroundColor="@android:color/holo_blue_dark"
            app:iconBackgroundCornerRadius="50dp"
            app:iconHeightPercent="0.75"
            app:iconLabelSeparation="0dp"
            app:iconPadding="5dp"
            app:rippleRadius="130"
            app:iconSource="@android:drawable/ic_menu_compass"
            app:labelText="@string/navigate_to_dest"
            app:labelTextColor="@android:color/holo_blue_dark"
            app:labelTextFontFamily="@font/ibm_flex_serif_bold"
            app:labelTextSize="10sp" />

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/upload_button"
            android:layout_width="50dp"
            android:layout_height="70dp"
            android:layout_marginStart="20dp"
            app:iconBackgroundColor="@android:color/holo_green_dark"
            app:iconBackgroundCornerRadius="30dp"
            app:iconHeightPercent="0.75"
            app:iconLabelSeparation="10dp"
            app:iconPadding="10dp"
            app:rippleRadius="100"
            app:iconSource="@android:drawable/arrow_up_float"
            app:labelText="@string/upload"
            app:labelTextColor="@android:color/darker_gray"
            app:labelTextSize="10sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/finding_button"
            android:layout_width="100dp"
            android:layout_height="170dp"
            android:layout_marginStart="20dp"
            app:iconBackgroundColor="@android:color/transparent"
            app:iconBackgroundCornerRadius="30dp"
            app:iconHeightPercent="0.7"
            app:iconLabelSeparation="-10dp"
            app:iconPadding="10dp"
            app:rippleRadius="200"
            app:iconSource="@android:drawable/ic_dialog_map"
            app:labelText="@string/finding"
            app:labelTextFontStyle="italic"
            app:labelTextSize="14sp" />

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/mark_important_button"
            android:layout_width="200dp"
            android:layout_height="150dp"
            android:layout_marginStart="20dp"
            app:iconBackgroundColor="@android:color/holo_purple"
            app:iconBackgroundCornerRadius="100dp"
            app:iconHeightPercent="0.7"
            app:iconLabelSeparation="0dp"
            app:iconPadding="25dp"
            app:rippleRadius="220"
            app:iconSource="@android:drawable/star_off"
            app:labelText="@string/mark_important"
            app:labelTextColor="@android:color/holo_purple"
            app:labelTextFontStyle="bold_italic"
            app:labelTextSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp">

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/on_star_button"
            android:layout_width="150dp"
            android:layout_height="100dp"
            android:layout_marginStart="20dp"
            app:iconBackgroundColor="@android:color/black"
            app:iconBackgroundCornerRadius="20dp"
            app:iconHeightPercent="0.7"
            app:iconLabelSeparation="0dp"
            app:iconPadding="5dp"
            app:rippleRadius="170"
            app:iconSource="@android:drawable/star_on"
            app:labelText="@string/star_on"
            app:labelTextSize="14sp" />

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/mute_button"
            android:layout_width="70dp"
            android:layout_height="200dp"
            android:layout_marginStart="20dp"
            app:iconBackgroundColor="@android:color/darker_gray"
            app:iconBackgroundCornerRadius="20dp"
            app:iconHeightPercent="0.7"
            app:iconLabelSeparation="0dp"
            app:iconPadding="5dp"
            app:rippleRadius="250"
            app:iconSource="@android:drawable/stat_notify_call_mute"
            app:labelText="@string/mute"
            app:labelTextSize="12sp" />

        <com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
            android:id="@+id/close_button"
            android:layout_width="100dp"
            android:layout_height="200dp"
            android:layout_marginStart="20dp"
            app:iconBackgroundColor="@android:color/black"
            app:iconBackgroundCornerRadius="20dp"
            app:iconHeightPercent="0.3"
            app:iconLabelSeparation="0dp"
            app:iconPadding="5dp"
            app:rippleRadius="220"
            app:iconSource="@android:drawable/ic_delete"
            app:iconWidth="60dp"
            app:labelText="@string/select_the_icon_to_close"
            app:labelTextColor="@android:color/holo_red_dark"
            app:labelTextSize="15sp" />

    </LinearLayout>

</LinearLayout>
