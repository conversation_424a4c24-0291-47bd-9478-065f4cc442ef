<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".HomeActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@color/colorPrimaryDark"
        android:theme="@style/MaterialTextInputLayoutTheme"
        app:title="@string/home_toolbar_title"
        app:titleTextColor="#fff" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="com.suryadigital.leo.libui.sampleapp.ComponentListFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


</LinearLayout>
