<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".ViewPagerDemoFragment">

    <ImageView
        android:contentDescription="@string/second_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@mipmap/second_image" />

    <Button
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_gravity="start|bottom"
        android:id="@+id/button"
        android:backgroundTint="@color/colorAccent"
        android:layout_marginBottom="20dp"
        android:text="@string/button" />

</FrameLayout>
