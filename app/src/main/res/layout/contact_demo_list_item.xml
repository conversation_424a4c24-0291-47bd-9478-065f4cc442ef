<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/list_row_selector"
    android:padding="10dp"
    tools:ignore="RtlHardcoded"
    tools:context=".contactpicker.PreviewContactListAdapter">

    <androidx.cardview.widget.CardView
        android:id="@+id/profile_cv"
        android:layout_width="@dimen/dimen_action_bar_size"
        android:layout_height="@dimen/dimen_action_bar_size"
        android:layout_marginLeft="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_2dp"
        android:layout_marginRight="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_2dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="@dimen/zero_dimen">

        <ImageView
            android:id="@+id/contact_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_person_black_24dp"
            android:contentDescription="@string/contact_photo"/>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/contact_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_10dp"
        android:layout_toRightOf="@id/profile_cv"
        android:textColor="@color/name_label_color"
        android:textStyle="bold"
        tools:text="John Wick" />

    <TextView
        android:id="@+id/mobile_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/contact_name"
        android:layout_toRightOf="@id/profile_cv"
        android:textColor="@color/mobile_number_label_color"
        tools:text="8811007865" />

</RelativeLayout>
