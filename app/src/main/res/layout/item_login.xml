<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:padding="10dp">

    <ImageView
        android:id="@+id/profilePicture"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:contentDescription="@string/profile_image"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginStart="10dp">

        <TextView
            android:id="@+id/userName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/textColor"
            android:textSize="20sp"
            tools:text="User 1"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnLogin"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="@string/login"
                style="?android:buttonStyle"/>

            <Button
                android:id="@+id/btnRegister"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="@string/log_out"
                android:visibility="gone"
                style="?android:buttonStyle"/>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
