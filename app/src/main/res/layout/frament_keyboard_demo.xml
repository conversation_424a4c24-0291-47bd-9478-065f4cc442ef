<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/output_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginBottom="30dp"
        android:gravity="center"
        android:importantForAutofill="no"
        android:maxLength="16"
        android:textColor="@color/colorPrimary"
        android:textSize="32sp"
        tools:ignore="LabelFor"
        tools:text="MWK 1234" />

    <com.suryadigital.leo.libui.numberkeyboard.NumberKeyboardView
        android:id="@+id/keyboard_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>
