<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/date_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:gravity="center"
        android:padding="16dp"
        android:textColor="@color/textColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent"
        app:layout_constraintWidth_max="500dp"
        tools:text="Time : 12/12/12" />

    <Button
        android:id="@+id/choose_date_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:backgroundTint="@color/blue_600"
        android:text="@string/choose_date"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="@+id/date_tv"
        app:layout_constraintStart_toStartOf="@+id/date_tv"
        app:layout_constraintTop_toBottomOf="@+id/date_tv" />

    <Button
        android:id="@+id/red_theme_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:backgroundTint="@color/red"
        android:text="@string/red_theme"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="@+id/green_theme_btn"
        app:layout_constraintStart_toStartOf="@+id/green_theme_btn"
        app:layout_constraintTop_toBottomOf="@+id/green_theme_btn" />

    <Button
        android:id="@+id/green_theme_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:backgroundTint="@color/green_300"
        android:text="@string/green_theme"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="@+id/choose_date_btn"
        app:layout_constraintStart_toStartOf="@+id/choose_date_btn"
        app:layout_constraintTop_toBottomOf="@+id/choose_date_btn" />

</androidx.constraintlayout.widget.ConstraintLayout>
