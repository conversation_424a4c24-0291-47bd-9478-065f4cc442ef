<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/dropdown_item_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="6sp"
        android:lines="1"
        android:layout_weight="1"
        android:paddingLeft="10dp"
        android:paddingTop="6dp"
        android:paddingRight="10dp"
        android:paddingBottom="6dp"
        android:textColor="@color/question_label_color"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="Delivered"/>

    <ImageButton
        android:layout_marginEnd="16dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:background="@android:color/transparent"
        android:src="@drawable/ic_arrow_drop_down"
        android:contentDescription="@string/dropdown_button_icon" />

</LinearLayout>
