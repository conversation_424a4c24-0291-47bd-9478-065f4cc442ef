<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/account_holder_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/name_label_color"
        android:textSize="16sp"
        android:textStyle="bold"
        android:paddingTop="8dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        tools:text="Prakash Sharma" />

    <TextView
        android:id="@+id/account_number"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textSize="12sp"
        android:textColor="@color/number_label_color"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingBottom="8dp"
        tools:text="**************" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/separator_color_black"
        android:alpha="0.5"/>
</LinearLayout>
