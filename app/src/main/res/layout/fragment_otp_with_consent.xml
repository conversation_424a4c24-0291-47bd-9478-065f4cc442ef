<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:gravity="center"
    android:orientation="horizontal">

    <com.suryadigital.leo.libui.otptextfield.OTPTextField
        android:id="@+id/otpView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        app:height="40dp"
        app:length="6"
        app:otp_box_background="@drawable/bg_otp_box"
        app:otp_box_background_active="@drawable/bg_otp_box_active"
        app:otp_box_background_error="@drawable/bg_otp_box_error"
        app:otp_box_background_success="@drawable/bg_otp_box_success"
        app:otp_text_size="16sp"
        app:width="32dp" />

    <Button
        android:id="@+id/resendBtn"
        style="?borderlessButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/resend"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold" />
</LinearLayout>
