<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:icon="@drawable/ic_language_toggle"
        app:showAsAction="always"
        android:title="@string/translate">

        <menu>
            <group>
                <item
                    android:id="@+id/localeEnglish"
                    android:title="@string/english"
                    app:showAsAction="ifRoom" />

                <item
                    android:id="@+id/localeNyanja"
                    android:title="@string/nyanja"
                    app:showAsAction="ifRoom"/>

            </group>
        </menu>

    </item>
</menu>
