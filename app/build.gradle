import java.nio.file.Paths

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    alias(libs.plugins.spotless)
}

def googleMapApiKey = new Properties()
googleMapApiKey.load(new FileInputStream(rootProject.file("googlemap.key")))

android {
    namespace = "com.suryadigital.leo.libui.sampleapp"
    compileSdk = compile_sdk_version.toInteger()
    buildToolsVersion = build_tools_version

    defaultConfig {
        applicationId "com.suryadigital.leo.libui.sampleapp"
        minSdkVersion min_sdk_version.toInteger()
        targetSdkVersion target_sdk_version.toInteger()
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lint {
        abortOnError true
        warningsAsErrors true
        disable 'GradleDependency',
                'MediaCapabilities',
                'OldTargetApi',
                'UnusedResources'
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key_debug"]
        }

        release {
            signingConfig signingConfigs.debug
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key_release"]
        }
    }

    bundle {
        language {
            /*
                Specifies that the app bundle should not support configuration APKs for language
                resources. These resources are instead packaged with each base and dynamic feature
                APK.
                Read More: https://developer.android.com/guide/playcore/feature-delivery/on-demand#lang_resources
             */
            enableSplit = false
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.constraintlayout
//    implementation libs.androidx.legacy.support
//    implementation libs.androidx.lifecycle.extensions
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.livedata.ktx
    implementation libs.google.play.services.auth
    implementation libs.androidx.preferences.ktx
    coreLibraryDesugaring libs.desugar
    implementation libs.google.play.services.barcode.scanning
    implementation libs.androidx.camera.camera2
    implementation libs.androidx.camera.lifecycle
    implementation libs.androidx.camera.view

    testImplementation libs.junit
    androidTestImplementation libs.androidx.test.ext.junit
    androidTestImplementation libs.androidx.test.espresso
    implementation project(path: ':libui')
}


apply from: Paths.get("${project.rootDir}", "spotless.gradle")
apply from: Paths.get("${project.rootDir}", "java.gradle")
