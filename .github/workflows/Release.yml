name: Release

on:
  push:
    branches:
      - master

jobs:
  Release:

    runs-on: ubuntu-latest

    env:
      GRADLE_OPTS: '"-Dorg.gradle.jvmargs=-Xms2048m -Xmx2048m" "-Dkotlin.daemon.jvmargs=-Xms2048m -Xmx2048m"'
      ARTIFACTORY_USERNAME: ${{ secrets.SURYA_DIGITAL_LEO_ARTIFACTS_USERNAME }}
      ARTIFACTORY_PASSWORD: ${{ secrets.SURYA_DIGITAL_LEO_ARTIFACTS_PASSWORD }}

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Setup Artifactory Properties
        run: |
          echo "username=${{ secrets.SURYA_DIGITAL_LEO_ARTIFACTS_USERNAME }}" > artifactory.properties
          echo "password=${{ secrets.SURYA_DIGITAL_LEO_ARTIFACTS_PASSWORD }}" >> artifactory.properties

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 21
          check-latest: true

      - name: Gradle caches
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Gradle tests
        run: ./gradlew clean check

      - name: Save test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test_reports
          path: libui/build/reports

      - name: Determine next version
        uses: gps/determine-next-version@master
        id: next_version
        with:
          GH_TOKEN: ${{ github.token }}

      - name: Publish packages
        env:
          NEXT_BUILD_VERSION: ${{steps.next_version.outputs.NEXT_BUILD_VERSION}}
        run: "./gradlew libui:build libui:publish"

      - name: Tag commit
        uses: gps/tag-commit@master
        with:
          GH_TOKEN: ${{ github.token }}
          TAG_NAME: v${{steps.next_version.outputs.NEXT_BUILD_VERSION}}
