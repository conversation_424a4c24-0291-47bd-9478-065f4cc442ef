name: CI

on: [pull_request]

jobs:
  Check:

    runs-on: ubuntu-latest

    env:
      GRADLE_OPTS: '"-Dorg.gradle.jvmargs=-Xms2048m -Xmx2048m" "-Dkotlin.daemon.jvmargs=-Xms2048m -Xmx2048m"'

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Setup Artifactory Properties
        run: |
          echo "username=${{ secrets.SURYA_DIGITAL_LEO_ARTIFACTS_USERNAME }}" > artifactory.properties
          echo "password=${{ secrets.SURYA_DIGITAL_LEO_ARTIFACTS_PASSWORD }}" >> artifactory.properties

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 21
          check-latest: true

      - name: Gradle caches
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Gradle Tests & Lint
        run: ./gradlew clean check

      - name: Run IntelliJ inspections
        uses: gps/intellij-android-inspections@master
        with:
          GH_TOKEN: ${{ github.token }}
          INSPECTIONS_FILE: AndroidUI.xml

      - name: Save inspection results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: idea_inspections
          path: target/idea_inspections

      - name: Save test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test_reports
          path: libui/build/reports/tests

      - name: Save lint results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: app-lint-results.html
          path: app/build/reports/lint-results-debug.html

      - name: Save lint results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: libui-lint-results.html
          path: libui/build/reports/lint-results-debug.html
